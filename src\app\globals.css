@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 210 40% 98%; /* Light Cool Gray */
    --foreground: 210 20% 25%; /* Dark Cool Gray for text */
    --card: 0 0% 100%; /* White */
    --card-foreground: 210 20% 25%;
    --popover: 0 0% 100%; /* White */
    --popover-foreground: 210 20% 25%;
    --primary: 202 68% 60%; /* Calming Blue - base: #A0D2EB hsl(202, 68%, 83%) */
    --primary-foreground: 202 60% 15%; /* Dark blue text for on-primary */
    --secondary: 90 42% 78%; /* Soft Green - base: #C4DFA8 hsl(90, 42%, 85%) */
    --secondary-foreground: 90 40% 20%; /* Dark green text */
    --muted: 210 30% 93%; /* Lighter gray */
    --muted-foreground: 210 15% 55%; /* Darker gray for muted text */
    --accent: 54 70% 70%; /* Muted Yellow - base: #F0E68C hsl(54, 70%, 74%) */
    --accent-foreground: 54 70% 15%; /* Dark yellow/brown text */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 20% 88%; /* Adjusted border */
    --input: 210 20% 92%; /* Adjusted input */
    --ring: 202 68% 60%; /* Primary color for focus rings */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar specific theme */
    --sidebar-background: 200 30% 96%; /* Very light gray/blue for sidebar */
    --sidebar-foreground: 202 50% 30%; /* Dark blue text for sidebar */
    --sidebar-primary: 202 68% 55%; /* Main interaction color in sidebar */
    --sidebar-primary-foreground: 0 0% 100%; /* White text on sidebar primary */
    --sidebar-accent: 202 68% 83%; /* User's #A0D2EB for active/hover states */
    --sidebar-accent-foreground: 202 50% 25%; /* Dark blue text on sidebar accent */
    --sidebar-border: 202 40% 85%; /* Border color for sidebar elements */
    --sidebar-ring: 202 68% 55%; /* Ring color for sidebar focus */
  }

  .dark {
    --background: 210 20% 12%; /* Dark Background */
    --foreground: 210 40% 98%; /* Light Text */
    --card: 210 20% 15%; /* Dark Card */
    --card-foreground: 210 40% 98%;
    --popover: 210 20% 15%; /* Dark Popover */
    --popover-foreground: 210 40% 98%;
    --primary: 202 68% 60%; /* Calming Blue (same as light for consistency or adjust lighter if needed) */
    --primary-foreground: 202 60% 15%;
    --secondary: 90 42% 45%; /* Darker Soft Green */
    --secondary-foreground: 90 40% 90%; /* Light green text */
    --muted: 210 20% 20%; /* Dark Muted */
    --muted-foreground: 210 15% 65%;
    --accent: 54 70% 55%; /* Darker Muted Yellow */
    --accent-foreground: 54 70% 90%; /* Light yellow text */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 20% 25%; /* Dark Border */
    --input: 210 20% 22%; /* Dark Input */
    --ring: 202 68% 60%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Dark Sidebar specific theme */
    --sidebar-background: 210 20% 10%; /* Very dark gray/blue for sidebar */
    --sidebar-foreground: 202 50% 80%; /* Lighter blue text for dark sidebar */
    --sidebar-primary: 202 68% 65%; /* Main interaction color in dark sidebar */
    --sidebar-primary-foreground: 202 60% 10%; /* Dark text on dark sidebar primary */
    --sidebar-accent: 202 68% 40%; /* Darker shade of #A0D2EB for active/hover */
    --sidebar-accent-foreground: 202 50% 90%; /* Light blue text on dark sidebar accent */
    --sidebar-border: 202 30% 25%; /* Border color for dark sidebar elements */
    --sidebar-ring: 202 68% 65%; /* Ring color for dark sidebar focus */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
