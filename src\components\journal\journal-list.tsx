
'use client';

import React from 'react';
import { useJournal } from '@/contexts/journal-context';
import { JournalEntryCard } from './journal-entry-card';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { BookOpen } from 'lucide-react';

export function JournalList() {
  const { journalEntries, isInitialized } = useJournal();

  if (!isInitialized) {
    // Show skeletons while context is initializing from localStorage
    return (
      <div className="space-y-6">
        {[...Array(2)].map((_, i) => ( // Show 2 skeletons as an example
          <Card className="shadow-md" key={`skeleton-${i}`}>
            <CardHeader>
              <Skeleton className="h-5 w-3/4 mb-2" /> {/* Title skeleton */}
              <Skeleton className="h-3 w-1/2" /> {/* Date skeleton */}
            </CardHeader>
            <CardContent className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-6 w-1/4" /> {/* Mood badge skeleton */}
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  if (journalEntries.length === 0) {
    return (
      <div className="text-center py-16 flex flex-col items-center justify-center min-h-[300px]">
        <BookOpen className="w-16 h-16 text-muted-foreground mb-4" />
        <p className="text-xl font-semibold text-muted-foreground">Your Journal is Empty</p>
        <p className="text-md text-muted-foreground">Start writing to fill these pages with your thoughts.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {journalEntries.map((entry) => (
        <JournalEntryCard key={entry.id} entry={entry} />
      ))}
    </div>
  );
}
