'use server';

/**
 * @fileOverview AI-powered personalized recommendations flow based on user journal entries.
 *
 * - getPersonalizedRecommendations - A function that provides personalized recommendations.
 * - PersonalizedRecommendationsInput - The input type for the getPersonalizedRecommendations function.
 * - PersonalizedRecommendationsOutput - The return type for the getPersonalizedRecommendations function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const PersonalizedRecommendationsInputSchema = z.object({
  journalEntries: z
    .string()
    .describe('The user journal entries to analyze for personalized recommendations.'),
  userInput: z
    .string()
    .optional()
    .describe('Any additional user input or preferences.'),
});
export type PersonalizedRecommendationsInput = z.infer<typeof PersonalizedRecommendationsInputSchema>;

const PersonalizedRecommendationsOutputSchema = z.object({
  recommendations: z
    .string()
    .describe(
      'Personalized recommendations for resources, exercises, and coping strategies based on the journal entries and user input.'
    ),
});
export type PersonalizedRecommendationsOutput = z.infer<typeof PersonalizedRecommendationsOutputSchema>;

export async function getPersonalizedRecommendations(
  input: PersonalizedRecommendationsInput
): Promise<PersonalizedRecommendationsOutput> {
  return personalizedRecommendationsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'personalizedRecommendationsPrompt',
  input: {schema: PersonalizedRecommendationsInputSchema},
  output: {schema: PersonalizedRecommendationsOutputSchema},
  prompt: `You are an AI assistant that provides personalized recommendations for mental health resources, exercises, and coping strategies.

  Based on the user's journal entries and any additional input, analyze their mood, potential triggers, and overall mental state.
  Provide tailored recommendations to help improve their well-being.

  Journal Entries: {{{journalEntries}}}
  User Input: {{{userInput}}}

  Recommendations:`,
});

const personalizedRecommendationsFlow = ai.defineFlow(
  {
    name: 'personalizedRecommendationsFlow',
    inputSchema: PersonalizedRecommendationsInputSchema,
    outputSchema: PersonalizedRecommendationsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
