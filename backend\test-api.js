// Simple API test script to verify backend functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testAPI() {
  console.log('🧪 Testing WELL Platform Backend API...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check:', healthResponse.data);
    console.log('');

    // Test 2: Register a new user
    console.log('2. Testing User Registration...');
    const registerData = {
      email: `test${Date.now()}@example.com`,
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User',
      role: 'PATIENT'
    };

    try {
      const registerResponse = await axios.post(`${BASE_URL}/api/auth/register`, registerData);
      console.log('✅ Registration successful:', registerResponse.data.message);
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('ℹ️  User already exists (expected if running multiple times)');
      } else {
        console.log('❌ Registration failed:', error.response?.data || error.message);
      }
    }
    console.log('');

    // Test 3: Login with default patient account
    console.log('3. Testing User Login...');
    const loginData = {
      email: '<EMAIL>',
      password: 'Patient123!@#'
    };

    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, loginData);
      console.log('✅ Login successful for patient account');
      
      const token = loginResponse.data.data.accessToken;
      console.log('🔑 Access token received');

      // Test 4: Get user profile
      console.log('\n4. Testing Protected Route (Get Profile)...');
      const profileResponse = await axios.get(`${BASE_URL}/api/auth/me`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Profile retrieved:', {
        name: `${profileResponse.data.data.firstName} ${profileResponse.data.data.lastName}`,
        email: profileResponse.data.data.email,
        role: profileResponse.data.data.role
      });

      // Test 5: Get psychiatrists list
      console.log('\n5. Testing Psychiatrists List...');
      const psychiatristsResponse = await axios.get(`${BASE_URL}/api/psychiatrists`);
      console.log('✅ Psychiatrists list retrieved:', {
        count: psychiatristsResponse.data.data.psychiatrists.length,
        total: psychiatristsResponse.data.data.pagination.total
      });

      // Test 6: Get forum posts
      console.log('\n6. Testing Forum Posts...');
      const forumResponse = await axios.get(`${BASE_URL}/api/forum/posts`);
      console.log('✅ Forum posts retrieved:', {
        count: forumResponse.data.data.posts.length,
        total: forumResponse.data.data.pagination.total
      });

    } catch (error) {
      if (error.response?.status === 401) {
        console.log('ℹ️  Login failed - this is expected if database is not set up');
        console.log('   Please set up MySQL database and run: npm run db:seed');
      } else {
        console.log('❌ Login failed:', error.response?.data || error.message);
      }
    }

  } catch (error) {
    console.log('❌ API test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the backend server is running:');
      console.log('   npm run dev:backend');
    }
  }

  console.log('\n🏁 API testing completed!');
  console.log('\n📚 Next steps:');
  console.log('1. Set up MySQL database');
  console.log('2. Configure .env file with database credentials');
  console.log('3. Run: npx prisma db push');
  console.log('4. Run: npm run db:seed');
  console.log('5. Test the full API functionality');
}

// Run the test
testAPI();
