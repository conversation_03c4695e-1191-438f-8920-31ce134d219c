
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { MessageSquare, Send, ThumbsUp, MessageCircle, Pin } from 'lucide-react';
import type { SpaceDiscussionPost } from '@/lib/types'; // Assuming SpaceDiscussionPost type exists
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

// Placeholder discussion data
const placeholderPosts: SpaceDiscussionPost[] = [
  {
    id: 'dp1',
    spaceId: 'space1',
    authorName: 'Host Sarah <PERSON>.',
    authorAvatarUrl: 'https://picsum.photos/seed/hostsarah/40/40',
    content: 'Welcome to Mindful Mornings! ✨ So excited to have you all here. What are you hoping to gain from this space?',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    isPinned: true,
    repliesCount: 5,
    reactions: { '👍': 12, '💖': 8 }
  },
  {
    id: 'dp2',
    spaceId: 'space1',
    authorName: 'WellnessUser123',
    authorAvatarUrl: 'https://picsum.photos/seed/userA/40/40',
    aiAuthorAvatarHint: "user avatar",
    content: 'Just joined! Looking forward to learning more about daily meditation practices.',
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
    repliesCount: 2,
    reactions: { '👍': 7 }
  },
];

export function DiscussionsSection({ spaceId }: { spaceId: string }) {
  const [newPostContent, setNewPostContent] = useState('');
  // In a real app, fetch posts for the given spaceId
  const [posts, setPosts] = useState<SpaceDiscussionPost[]>(placeholderPosts.filter(p => p.spaceId === spaceId || placeholderPosts));
  const { toast } = useToast();

  const handlePostSubmit = () => {
    if (!newPostContent.trim()) return;
    const newPost: SpaceDiscussionPost = {
      id: `dp${Date.now()}`,
      spaceId,
      authorName: 'Current User (You)', // Placeholder
      authorAvatarUrl: 'https://picsum.photos/seed/currentUser/40/40',
      aiAuthorAvatarHint: "profile photo",
      content: newPostContent,
      timestamp: new Date().toISOString(),
    };
    setPosts(prevPosts => [newPost, ...prevPosts]);
    setNewPostContent('');
    toast({ title: "Post Submitted", description: "Your message has been added to the discussion." });
  };

  return (
    <Card className="shadow-md">
      <CardHeader>
        <CardTitle className="flex items-center text-2xl">
          <MessageSquare className="mr-2 h-6 w-6 text-primary" />
          Discussions & Group Chat
        </CardTitle>
        <CardDescription>
          Engage with the community, share your thoughts, and ask questions.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* New Post Input */}
        <div className="space-y-2">
          <Textarea
            placeholder="Share something with the space..."
            value={newPostContent}
            onChange={(e) => setNewPostContent(e.target.value)}
            rows={3}
          />
          <div className="flex justify-end">
            <Button onClick={handlePostSubmit} disabled={!newPostContent.trim()}>
              <Send className="mr-2 h-4 w-4" /> Post
            </Button>
          </div>
        </div>

        {/* Posts List */}
        <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2">
          {posts.length > 0 ? (
            posts.map(post => (
              <Card key={post.id} className={`p-4 ${post.isPinned ? 'bg-primary/5 border-primary/30' : 'bg-muted/30'}`}>
                <div className="flex items-start gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={post.authorAvatarUrl} alt={post.authorName} data-ai-hint={post.aiAuthorAvatarHint || "user photo"}/>
                    <AvatarFallback>{post.authorName.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex justify-between items-center">
                        <p className="font-semibold text-sm">{post.authorName}</p>
                        {post.isPinned && <Badge variant="outline" className="text-xs border-primary text-primary"><Pin className="mr-1 h-3 w-3"/> Pinned</Badge>}
                    </div>
                    <p className="text-xs text-muted-foreground mb-1">
                      {new Date(post.timestamp).toLocaleString()}
                    </p>
                    <p className="text-sm text-foreground/90 whitespace-pre-wrap">{post.content}</p>
                    <div className="mt-2 flex items-center gap-4 text-xs text-muted-foreground">
                        <Button variant="ghost" size="sm" className="p-1 h-auto">
                            <ThumbsUp className="mr-1 h-3 w-3" /> Like {post.reactions?.['👍'] || 0}
                        </Button>
                        <Button variant="ghost" size="sm" className="p-1 h-auto">
                           <MessageCircle className="mr-1 h-3 w-3" /> Reply {post.repliesCount || 0}
                        </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <p className="text-muted-foreground text-center py-4">No discussions yet. Start one!</p>
          )}
        </div>
      </CardContent>
       <CardFooter>
        <p className="text-xs text-muted-foreground">
          Discussion boards support real-time chat, comments, reactions, and pinned messages by hosts.
        </p>
      </CardFooter>
    </Card>
  );
}
