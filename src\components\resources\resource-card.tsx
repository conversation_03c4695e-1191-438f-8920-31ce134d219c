import React from 'react';
import Image from 'next/image';
import type { Resource } from '@/lib/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ExternalLink, FileText, Video, BookOpen } from 'lucide-react';

interface ResourceCardProps {
  resource: Resource;
}

const ResourceIcon = ({ type }: { type: Resource['type'] }) => {
  switch (type) {
    case 'article': return <FileText className="h-5 w-5 text-primary" />;
    case 'video': return <Video className="h-5 w-5 text-primary" />;
    case 'guide': return <BookOpen className="h-5 w-5 text-primary" />;
    default: return <FileText className="h-5 w-5 text-primary" />;
  }
};

export function ResourceCard({ resource }: ResourceCardProps) {
  return (
    <Card className="flex flex-col h-full shadow-md hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        {resource.imageUrl && (
          <div className="relative aspect-video mb-4 rounded-t-md overflow-hidden">
            <Image
              src={resource.imageUrl}
              alt={resource.title}
              layout="fill"
              objectFit="cover"
              data-ai-hint={resource.aiHint || "resource image"}
            />
          </div>
        )}
        <div className="flex items-center gap-2 mb-1">
          <ResourceIcon type={resource.type} />
          <CardTitle className="text-lg font-semibold leading-tight">{resource.title}</CardTitle>
        </div>
        <CardDescription className="text-sm text-muted-foreground flex-grow min-h-[3em]">{resource.description}</CardDescription>
      </CardHeader>
      <CardFooter className="mt-auto">
        <Button variant="outline" asChild className="w-full">
          <a href={resource.link} target="_blank" rel="noopener noreferrer">
            View Resource <ExternalLink className="ml-2 h-4 w-4" />
          </a>
        </Button>
      </CardFooter>
    </Card>
  );
}
