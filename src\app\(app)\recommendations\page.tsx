'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useJournal } from '@/contexts/journal-context';
import { getPersonalizedRecommendations, type PersonalizedRecommendationsOutput } from '@/ai/flows/personalized-recommendations';
import { RecommendationsDisplay } from '@/components/recommendations/recommendations-display';
import { Loader2, Sparkles, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function RecommendationsPage() {
  const { journalEntries, getJournalEntriesText } = useJournal();
  const [userInput, setUserInput] = useState('');
  const [recommendations, setRecommendations] = useState<PersonalizedRecommendationsOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGetRecommendations = async () => {
    setIsLoading(true);
    setError(null);
    setRecommendations(null);

    if (journalEntries.length === 0 && !userInput.trim()) {
      setError("Please provide some journal entries or input to get recommendations.");
      setIsLoading(false);
      return;
    }
    
    try {
      const entriesText = getJournalEntriesText();
      const result = await getPersonalizedRecommendations({ 
        journalEntries: entriesText,
        userInput: userInput.trim() || undefined,
       });
      setRecommendations(result);
    } catch (err) {
      console.error("Error getting recommendations:", err);
      setError("Failed to generate recommendations. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 max-w-3xl">
      <div className="flex items-center justify-center mb-10">
        <Sparkles className="h-10 w-10 text-primary mr-3" />
        <h1 className="text-3xl font-bold text-center text-primary">Personalized Recommendations</h1>
      </div>
      <p className="text-center text-muted-foreground mb-8">
        Receive tailored suggestions for resources, exercises, and coping strategies based on your journal and current needs.
      </p>

      <Card className="mb-8 shadow-md">
        <CardHeader>
          <CardTitle>Generate Recommendations</CardTitle>
          <CardDescription>
            Your journal entries will be used for recommendations. You can also add specific requests or context below.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="userInput">Additional context or requests (optional)</Label>
            <Textarea
              id="userInput"
              placeholder="e.g., 'I'm looking for ways to manage stress at work' or 'I need help with sleep'"
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              rows={3}
            />
          </div>
          <Button 
            onClick={handleGetRecommendations} 
            disabled={isLoading || (journalEntries.length === 0 && !userInput.trim())} 
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              'Get My Recommendations'
            )}
          </Button>
        </CardContent>
      </Card>


      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {recommendations && <RecommendationsDisplay recommendations={recommendations} />}
      
      {!isLoading && !recommendations && !error && (journalEntries.length > 0 || userInput.trim()) && (
        <p className="text-center text-muted-foreground mt-8">Click "Get My Recommendations" for personalized advice.</p>
      )}
      {!isLoading && !recommendations && !error && journalEntries.length === 0 && !userInput.trim() && (
        <p className="text-center text-muted-foreground mt-8">Add journal entries or provide some input to enable recommendations.</p>
      )}
    </div>
  );
}
