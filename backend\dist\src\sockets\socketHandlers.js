"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeSocketHandlers = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const server_1 = require("../../server");
const connectedUsers = new Map();
const userSockets = new Map();
const initializeSocketHandlers = (io) => {
    io.use(async (socket, next) => {
        try {
            const token = socket.handshake.auth.token;
            if (!token) {
                return next(new Error('Authentication token required'));
            }
            if (!process.env.JWT_SECRET) {
                return next(new Error('JWT_SECRET not configured'));
            }
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
            const user = await server_1.prisma.user.findUnique({
                where: { id: decoded.userId },
                select: {
                    id: true,
                    email: true,
                    role: true,
                    status: true,
                },
            });
            if (!user || user.status !== 'ACTIVE') {
                return next(new Error('Invalid user or inactive account'));
            }
            socket.user = {
                id: user.id,
                email: user.email,
                role: user.role,
            };
            next();
        }
        catch (error) {
            next(new Error('Invalid authentication token'));
        }
    });
    io.on('connection', (socket) => {
        if (!socket.user)
            return;
        const userId = socket.user.id;
        console.log(`User ${userId} connected with socket ${socket.id}`);
        connectedUsers.set(socket.id, {
            userId,
            socketId: socket.id,
            role: socket.user.role,
            isOnline: true,
            lastSeen: new Date(),
        });
        userSockets.set(userId, socket.id);
        socket.join(`user:${userId}`);
        socket.join(`role:${socket.user.role}`);
        socket.broadcast.emit('user:online', { userId, isOnline: true });
        socket.on('session:join', async (data) => {
            try {
                const session = await server_1.prisma.session.findUnique({
                    where: { id: data.sessionId },
                    include: {
                        patient: { select: { id: true, firstName: true, lastName: true } },
                        psychiatrist: { select: { id: true, firstName: true, lastName: true } },
                    },
                });
                if (!session) {
                    socket.emit('error', { message: 'Session not found' });
                    return;
                }
                if (session.patientId !== userId && session.psychiatristId !== userId) {
                    socket.emit('error', { message: 'Unauthorized to join this session' });
                    return;
                }
                socket.join(`session:${data.sessionId}`);
                socket.to(`session:${data.sessionId}`).emit('session:user_joined', {
                    userId,
                    userName: `${socket.user.email}`,
                });
                socket.emit('session:joined', { sessionId: data.sessionId });
            }
            catch (error) {
                console.error('Error joining session:', error);
                socket.emit('error', { message: 'Failed to join session' });
            }
        });
        socket.on('session:leave', (data) => {
            socket.leave(`session:${data.sessionId}`);
            socket.to(`session:${data.sessionId}`).emit('session:user_left', {
                userId,
            });
        });
        socket.on('message:send', async (data) => {
            try {
                if (!data.content || data.content.trim().length === 0) {
                    socket.emit('error', { message: 'Message content is required' });
                    return;
                }
                const message = await server_1.prisma.message.create({
                    data: {
                        senderId: userId,
                        receiverId: data.receiverId,
                        sessionId: data.sessionId,
                        content: data.content.trim(),
                        messageType: data.messageType || 'TEXT',
                    },
                    include: {
                        sender: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                profileImage: true,
                            },
                        },
                    },
                });
                const chatMessage = {
                    id: message.id,
                    senderId: message.senderId,
                    receiverId: message.receiverId,
                    sessionId: message.sessionId,
                    content: message.content,
                    messageType: message.messageType,
                    timestamp: message.createdAt,
                    isRead: false,
                };
                if (data.sessionId) {
                    io.to(`session:${data.sessionId}`).emit('message:received', {
                        ...chatMessage,
                        sender: message.sender,
                    });
                }
                else if (data.receiverId) {
                    const receiverSocketId = userSockets.get(data.receiverId);
                    if (receiverSocketId) {
                        io.to(receiverSocketId).emit('message:received', {
                            ...chatMessage,
                            sender: message.sender,
                        });
                    }
                    socket.emit('message:sent', { messageId: message.id });
                }
            }
            catch (error) {
                console.error('Error sending message:', error);
                socket.emit('error', { message: 'Failed to send message' });
            }
        });
        socket.on('typing:start', (data) => {
            if (data.sessionId) {
                socket.to(`session:${data.sessionId}`).emit('typing:start', { userId });
            }
            else if (data.receiverId) {
                const receiverSocketId = userSockets.get(data.receiverId);
                if (receiverSocketId) {
                    io.to(receiverSocketId).emit('typing:start', { userId });
                }
            }
        });
        socket.on('typing:stop', (data) => {
            if (data.sessionId) {
                socket.to(`session:${data.sessionId}`).emit('typing:stop', { userId });
            }
            else if (data.receiverId) {
                const receiverSocketId = userSockets.get(data.receiverId);
                if (receiverSocketId) {
                    io.to(receiverSocketId).emit('typing:stop', { userId });
                }
            }
        });
        socket.on('message:read', async (data) => {
            try {
                await server_1.prisma.message.update({
                    where: { id: data.messageId },
                    data: { isRead: true, readAt: new Date() },
                });
                const message = await server_1.prisma.message.findUnique({
                    where: { id: data.messageId },
                    select: { senderId: true },
                });
                if (message) {
                    const senderSocketId = userSockets.get(message.senderId);
                    if (senderSocketId) {
                        io.to(senderSocketId).emit('message:read', {
                            messageId: data.messageId,
                            readBy: userId,
                        });
                    }
                }
            }
            catch (error) {
                console.error('Error marking message as read:', error);
            }
        });
        socket.on('session:status', async (data) => {
            try {
                const session = await server_1.prisma.session.findUnique({
                    where: { id: data.sessionId },
                });
                if (!session) {
                    socket.emit('error', { message: 'Session not found' });
                    return;
                }
                if (session.patientId !== userId && session.psychiatristId !== userId) {
                    socket.emit('error', { message: 'Unauthorized to update session status' });
                    return;
                }
                const updateData = {};
                if (data.status === 'started') {
                    updateData.status = 'IN_PROGRESS';
                    updateData.startedAt = new Date();
                }
                else if (data.status === 'ended') {
                    updateData.status = 'COMPLETED';
                    updateData.endedAt = new Date();
                }
                await server_1.prisma.session.update({
                    where: { id: data.sessionId },
                    data: updateData,
                });
                io.to(`session:${data.sessionId}`).emit('session:status_updated', {
                    sessionId: data.sessionId,
                    status: data.status,
                    timestamp: new Date(),
                });
            }
            catch (error) {
                console.error('Error updating session status:', error);
                socket.emit('error', { message: 'Failed to update session status' });
            }
        });
        socket.on('notification:send', (data) => {
            const targetSocketId = userSockets.get(data.targetUserId);
            if (targetSocketId) {
                io.to(targetSocketId).emit('notification:received', data.notification);
            }
        });
        socket.on('disconnect', () => {
            console.log(`User ${userId} disconnected`);
            connectedUsers.delete(socket.id);
            userSockets.delete(userId);
            socket.broadcast.emit('user:offline', { userId, isOnline: false });
        });
        socket.emit('connected', {
            userId,
            socketId: socket.id,
            timestamp: new Date(),
        });
    });
    const sendNotificationToUser = (userId, notification) => {
        const socketId = userSockets.get(userId);
        if (socketId) {
            io.to(socketId).emit('notification:received', notification);
        }
    };
    const getOnlineUsers = () => {
        return Array.from(connectedUsers.values());
    };
    const isUserOnline = (userId) => {
        return userSockets.has(userId);
    };
    return {
        sendNotificationToUser,
        getOnlineUsers,
        isUserOnline,
    };
};
exports.initializeSocketHandlers = initializeSocketHandlers;
//# sourceMappingURL=socketHandlers.js.map