
'use client';

import type { JournalEntry, Mood } from '@/lib/types';
import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';

interface JournalContextType {
  journalEntries: JournalEntry[];
  addJournalEntry: (entry: Omit<JournalEntry, 'id' | 'date'>) => void;
  deleteJournalEntry: (id: string) => void;
  getJournalEntriesText: () => string;
  isInitialized: boolean; // Expose initialization status
}

const JournalContext = createContext<JournalContextType | undefined>(undefined);

const LOCAL_STORAGE_KEY = 'well_journal_entries';

export const JournalProvider = ({ children }: { children: ReactNode }) => {
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const [isInitialized, setIsInitialized] = useState(false); 

  useEffect(() => {
    const savedEntries = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedEntries) {
      try {
        const parsedEntries = JSON.parse(savedEntries);
        if (Array.isArray(parsedEntries)) {
          setJournalEntries(parsedEntries);
        } else {
          console.error("Invalid data in localStorage for journal entries.");
          setJournalEntries([]); 
        }
      } catch (error) {
        console.error("Error parsing journal entries from localStorage:", error);
        setJournalEntries([]); 
      }
    }
    setIsInitialized(true); 
  }, []); 

  useEffect(() => {
    if (isInitialized) { 
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(journalEntries));
    }
  }, [journalEntries, isInitialized]); 

  const addJournalEntry = (entry: Omit<JournalEntry, 'id' | 'date'>) => {
    const newEntry: JournalEntry = {
      ...entry,
      id: Date.now().toString(),
      date: new Date().toISOString(),
    };
    setJournalEntries(prevEntries => [newEntry, ...prevEntries]);
  };

  const deleteJournalEntry = (id: string) => {
    setJournalEntries(prevEntries => prevEntries.filter(entry => entry.id !== id));
  };

  const getJournalEntriesText = () => {
    return journalEntries
      .map(entry => `Date: ${new Date(entry.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}\nTitle: ${entry.title}\nMood: ${entry.mood}\nContent: ${entry.content}\n\n`)
      .join('');
  };

  return (
    <JournalContext.Provider value={{ journalEntries, addJournalEntry, deleteJournalEntry, getJournalEntriesText, isInitialized }}>
      {children}
    </JournalContext.Provider>
  );
};

export const useJournal = (): JournalContextType => {
  const context = useContext(JournalContext);
  if (context === undefined) {
    throw new Error('useJournal must be used within a JournalProvider');
  }
  return context;
};

