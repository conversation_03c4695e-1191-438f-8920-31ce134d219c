
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { PLACEHOLDER_THERAPISTS } from '@/lib/constants';
import type { TherapistProfile } from '@/lib/types';
import { Search, CalendarDays, Briefcase, Info, UserCircle2, X } from 'lucide-react'; // Removed DollarSign
import { useToast } from '@/hooks/use-toast';

function TherapistCard({ therapist }: { therapist: TherapistProfile }) {
  const { toast } = useToast();
  const fallbackInitials = therapist.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'TP';

  const handleBookAppointment = () => {
    toast({
      title: "Booking Simulated",
      description: `Appointment request for ${therapist.name} sent. (This is a placeholder action)`,
    });
  };

  return (
    <Card className="shadow-md hover:shadow-lg transition-shadow w-full">
      <CardHeader className="flex flex-col items-center text-center sm:flex-row sm:text-left sm:items-start gap-4">
        <Avatar className="h-24 w-24 border-2 border-primary">
          <AvatarImage src={therapist.avatarUrl} alt={therapist.name} data-ai-hint={therapist.aiHint || "therapist photo"}/>
          <AvatarFallback>{fallbackInitials}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <CardTitle className="text-xl">{therapist.name}</CardTitle>
          <div className="flex flex-wrap gap-1 mt-1 justify-center sm:justify-start">
            {therapist.specialties.map((spec) => (
              <Badge key={spec} variant="secondary">{spec}</Badge>
            ))}
          </div>
          {therapist.hourlyRate && (
             <p className="text-sm text-muted-foreground mt-1 flex items-center justify-center sm:justify-start">
                Birr {therapist.hourlyRate}/hour
            </p>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground text-sm line-clamp-3">{therapist.bio}</p>
      </CardContent>
      <CardFooter className="flex justify-center sm:justify-end">
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="default">
              <Info className="mr-2 h-4 w-4" /> View Profile & Book
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[525px]">
            <DialogHeader>
              <div className="flex items-center space-x-4 mb-4">
                <Avatar className="h-20 w-20 border-2 border-primary">
                  <AvatarImage src={therapist.avatarUrl} alt={therapist.name} data-ai-hint={therapist.aiHint || "therapist photo"}/>
                  <AvatarFallback>{fallbackInitials}</AvatarFallback>
                </Avatar>
                <div>
                    <DialogTitle className="text-2xl">{therapist.name}</DialogTitle>
                    <div className="flex flex-wrap gap-1 mt-1">
                        {therapist.specialties.map((spec) => (
                        <Badge key={spec} variant="secondary">{spec}</Badge>
                        ))}
                    </div>
                </div>
              </div>
              <DialogDescription className="text-left">
                {therapist.bio}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 my-4">
                <div className="flex items-start">
                    <Briefcase className="h-5 w-5 text-primary mr-3 mt-1"/>
                    <div>
                        <h4 className="font-semibold">Specialties</h4>
                        <p className="text-sm text-muted-foreground">{therapist.specialties.join(', ')}</p>
                    </div>
                </div>
                <div className="flex items-start">
                    <CalendarDays className="h-5 w-5 text-primary mr-3 mt-1"/>
                    <div>
                        <h4 className="font-semibold">Availability</h4>
                        <p className="text-sm text-muted-foreground">{therapist.availability}</p>
                    </div>
                </div>
                 {therapist.hourlyRate && (
                    <div className="flex items-start">
                        {/* Removed DollarSign icon, using text "Birr" */}
                        <span className="text-primary mr-3 mt-1 font-semibold w-5 h-5 flex items-center justify-center"></span> {/* Placeholder for spacing if needed, or adjust layout */}
                        <div>
                            <h4 className="font-semibold">Rate</h4>
                            <p className="text-sm text-muted-foreground">Birr {therapist.hourlyRate} per hour</p>
                        </div>
                    </div>
                )}
            </div>
            <DialogFooter className="sm:justify-between">
                <DialogClose asChild>
                    <Button type="button" variant="outline">
                        Close
                    </Button>
                </DialogClose>
                <Button type="button" onClick={handleBookAppointment}>
                   Request Appointment
                </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  );
}

export function ConnectTherapists() {
  const [searchTerm, setSearchTerm] = useState('');
  const filteredTherapists = PLACEHOLDER_THERAPISTS.filter(therapist =>
    therapist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    therapist.specialties.some(spec => spec.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-8">
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl flex items-center gap-2"><UserCircle2 className="h-7 w-7 text-primary"/>Find a Therapist</CardTitle>
          <CardDescription>Search for licensed therapists based on your preferences. All interactions are placeholders.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Input
              type="text"
              placeholder="Search by name or specialty (e.g., Anxiety, CBT)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      {filteredTherapists.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredTherapists.map((therapist) => (
            <TherapistCard key={therapist.id} therapist={therapist} />
          ))}
        </div>
      ) : (
        <p className="text-center text-muted-foreground py-10">No therapists match your search criteria.</p>
      )}
    </div>
  );
}

