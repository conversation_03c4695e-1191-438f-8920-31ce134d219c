
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Users, Library, Brain, Zap } from 'lucide-react'; // Using Brain, Zap is a fallback

const features = [
  {
    icon: <Feather className="h-8 w-8 text-primary" />,
    title: 'Intelligent Journaling',
    description: 'Uncover patterns in your thoughts and emotions with AI-powered analysis of your journal entries.',
  },
  {
    icon: <Sparkles className="h-8 w-8 text-primary" />,
    title: 'Personalized Guidance',
    description: 'Receive tailored recommendations for exercises, resources, and coping strategies based on your unique needs.',
  },
  {
    icon: <Users className="h-8 w-8 text-primary" />,
    title: 'Supportive Community',
    description: 'Connect, share, and grow with like-minded individuals in a safe and anonymous space.',
  },
  {
    icon: <Library className="h-8 w-8 text-primary" />,
    title: 'Curated Resources',
    description: 'Access a library of articles, videos, and guides to support your mental fitness journey.',
  },
  {
    icon: <Brain className="h-8 w-8 text-primary" />, 
    title: 'Mood & Insight Tracking',
    description: 'Visualize your emotional landscape and gain a deeper understanding of your triggers and progress.',
  },
  {
    icon: <Zap className="h-8 w-8 text-primary" />, 
    title: 'Mindful Exercises',
    description: 'Practice guided meditations and mindfulness techniques to reduce stress and improve focus.',
  },
];

export function FeaturesSection() {
  return (
    <section id="features" className="py-16 md:py-24 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Discover a New Path to Well-being
          </h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            WELL offers a suite of tools designed to empower you on your mental wellness journey.
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature) => (
            <Card key={feature.title} className="shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="flex flex-row items-start gap-4 space-y-0 pb-4">
                <div className="bg-primary/10 p-3 rounded-lg">
                  {feature.icon}
                </div>
                <CardTitle className="text-xl font-semibold mt-0 pt-1">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

