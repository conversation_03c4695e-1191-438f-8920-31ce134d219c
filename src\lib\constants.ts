
import type { Resource, ForumPost, TherapistProfile, GuidedSession, CBTExercise, CrisisResourceItem, Space, PlaceholderClient, PlaceholderAppointment } from './types';
import { BookOpen, Library, Users, Lightbulb, Sparkles, Feather, UserCircle2, Brain, Settings, HeartHandshake, Users2, PlayCircle, Puzzle, ShieldAlert, LineChart, Activity, BrainCog, Youtube, Headphones, ClipboardEdit, AlertOctagon, PieChart, UsersRound, Network } from 'lucide-react';

export const APP_NAME = "WELL";

export const NAVIGATION_LINKS = [
  { href: '/journal', label: 'Journal', icon: Feather },
  { href: '/insights', label: 'AI Insights', icon: Brain },
  { href: '/recommendations', label: 'Recommendations', icon: Sparkles },
  { href: '/resources', label: 'Resources', icon: Library },
  { href: '/community', label: 'Community', icon: Users },
  { href: '/therapy', label: 'Therapy', icon: HeartHandshake },
  { href: '/people', label: 'People', icon: Network }, // Changed icon here
  // { href: '/profile', label: 'Profile', icon: UserCircle2 }, // Removed from sidebar
];

export const THERAPY_TAB_LINKS = [
  { value: 'connect', label: 'Find a Therapist', icon: Users2 },
  { value: 'sessions', label: 'Guided Sessions', icon: Youtube },
  { value: 'cbt', label: 'CBT Exercises', icon: BrainCog },
  { value: 'crisis', label: 'Crisis Support', icon: ShieldAlert },
  { value: 'progress', label: 'My Progress', icon: LineChart },
];

export const PLACEHOLDER_RESOURCES: Resource[] = [
  {
    id: '1',
    type: 'article',
    title: 'Understanding Anxiety: A Comprehensive Guide',
    description: 'Learn about the common symptoms, causes, and treatments for anxiety disorders.',
    link: '#',
    imageUrl: 'https://picsum.photos/seed/resource1/400/250',
    aiHint: 'calm nature',
  },
  {
    id: '2',
    type: 'video',
    title: 'Mindfulness Meditation for Beginners',
    description: 'A 10-minute guided meditation to help you relax and stay present.',
    link: '#',
    imageUrl: 'https://picsum.photos/seed/resource2/400/250',
    aiHint: 'serene yoga',
  },
  {
    id: '3',
    type: 'guide',
    title: 'Coping Strategies for Stressful Times',
    description: 'Practical tips and techniques to manage stress effectively in your daily life.',
    link: '#',
    imageUrl: 'https://picsum.photos/seed/resource3/400/250',
    aiHint: 'peaceful landscape',
  },
  {
    id: '4',
    type: 'article',
    title: 'The Importance of Sleep for Mental Health',
    description: 'Discover how quality sleep impacts your mood, cognitive function, and overall well-being.',
    link: '#',
    imageUrl: 'https://picsum.photos/seed/resource4/400/250',
    aiHint: 'cozy bedroom',
  },
];

export const PLACEHOLDER_FORUM_POSTS: ForumPost[] = [
  {
    id: 'fp1',
    title: 'Feeling overwhelmed with work lately. Any tips?',
    content: 'I\'ve been struggling to keep up with deadlines and it\'s taking a toll on my mental health. Has anyone else experienced this and found ways to cope?',
    author: 'User_Alex',
    avatarUrl: 'https://picsum.photos/seed/avatar1/100/100',
    aiHint: 'friendly person',
    date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
  },
  {
    id: 'fp2',
    title: 'Small victories to celebrate!',
    content: 'Today I managed to go for a walk even though I didn\'t feel like it. It\'s a small step, but I\'m proud of myself. What are your small victories today?',
    author: 'User_Sam',
    avatarUrl: 'https://picsum.photos/seed/avatar2/100/100',
    aiHint: 'smiling face',
    date: new Date(Date.now() - 2 * 86400000).toISOString(), // 2 days ago
  },
  {
    id: 'fp3',
    title: 'Looking for book recommendations on mindfulness.',
    content: 'Hi everyone, I\'m new to mindfulness and would love some book recommendations to get started. What helped you the most?',
    author: 'User_Casey',
    avatarUrl: 'https://picsum.photos/seed/avatar3/100/100',
    aiHint: 'thoughtful individual',
    date: new Date(Date.now() - 3 * 86400000).toISOString(), // 3 days ago
  },
];


export const PLACEHOLDER_THERAPISTS: TherapistProfile[] = [
  {
    id: 't1',
    name: 'Dr. Evelyn Reed',
    specialties: ['Anxiety', 'Depression', 'Trauma'],
    bio: 'Dr. Reed is a compassionate therapist with 10 years of experience helping individuals navigate life\'s challenges.',
    avatarUrl: 'https://picsum.photos/seed/therapist1/100/100',
    aiHint: 'professional woman',
    availability: 'Mon, Wed, Fri mornings',
    hourlyRate: 150,
  },
  {
    id: 't2',
    name: 'Mr. Samuel Green',
    specialties: ['Stress Management', 'Relationships', 'CBT'],
    bio: 'Samuel focuses on providing practical tools and strategies for managing stress and improving relationships.',
    avatarUrl: 'https://picsum.photos/seed/therapist2/100/100',
    aiHint: 'professional man',
    availability: 'Tue, Thu afternoons',
    hourlyRate: 120,
  },
  {
    id: 't3',
    name: 'Dr. Alisha Khan',
    specialties: ['Grief Counseling', 'Self-Esteem', 'Mindfulness'],
    bio: 'Dr. Khan offers a supportive space for healing and personal growth, integrating mindfulness practices.',
    avatarUrl: 'https://picsum.photos/seed/therapist3/100/100',
    aiHint: 'therapist smiling',
    availability: 'Mon evenings, Sat mornings',
    hourlyRate: 140,
  },
];

export const PLACEHOLDER_GUIDED_SESSIONS: GuidedSession[] = [
  {
    id: 'gs1',
    title: '10-Minute Morning Meditation for Anxiety',
    category: 'Anxiety',
    type: 'audio',
    duration: '10 min',
    description: 'Start your day with a calming meditation to ease anxiety and set a positive tone.',
    thumbnailUrl: 'https://picsum.photos/seed/sessionA1/300/180',
    aiHint: 'sunrise meditation',
    sourceUrl: '#',
  },
  {
    id: 'gs2',
    title: 'Stress Relief Breathing Exercise',
    category: 'Stress Management',
    type: 'video',
    duration: '5 min',
    description: 'Learn a simple breathing technique to quickly reduce stress and find your center.',
    thumbnailUrl: 'https://picsum.photos/seed/sessionB2/300/180',
    aiHint: 'calm breathing',
    sourceUrl: '#',
  },
  {
    id: 'gs3',
    title: 'Guided Journey for Deep Sleep',
    category: 'Sleep',
    type: 'audio',
    duration: '20 min',
    description: 'A soothing audio session to help you relax your mind and body for a restful night\'s sleep.',
    thumbnailUrl: 'https://picsum.photos/seed/sessionC3/300/180',
    aiHint: 'night sky',
    sourceUrl: '#',
  },
  {
    id: 'gs4',
    title: 'Building Self-Esteem: Affirmations',
    category: 'Self-Esteem',
    type: 'video',
    duration: '12 min',
    description: 'Positive affirmations to help you build confidence and self-worth.',
    thumbnailUrl: 'https://picsum.photos/seed/sessionD4/300/180',
    aiHint: 'confident person',
    sourceUrl: '#',
  },
];

export const PLACEHOLDER_CBT_EXERCISES: CBTExercise[] = [
  {
    id: 'cbt1',
    title: 'Challenging Negative Thoughts',
    description: 'Learn to identify, challenge, and reframe unhelpful negative thought patterns.',
    category: 'Thought Reframing',
    interactive: true,
    iconName: 'BrainCog',
  },
  {
    id: 'cbt2',
    title: 'Gratitude Journaling Prompt',
    description: 'Reflect on three things you are grateful for today and why.',
    category: 'Positive Psychology',
    interactive: true,
    iconName: 'Feather',
  },
  {
    id: 'cbt3',
    title: 'Behavioral Activation: Small Steps',
    description: 'Plan one small, enjoyable, or meaningful activity to do today.',
    category: 'Behavioral Activation',
    interactive: false, // Could be interactive with planning tool
    iconName: 'Activity',
  },
   {
    id: 'cbt4',
    title: 'Worry Exposure Log',
    description: 'Gradually face your worries by writing them down and assessing outcomes.',
    category: 'Exposure Therapy',
    interactive: true,
    iconName: 'ClipboardEdit',
  },
];

export const PLACEHOLDER_CRISIS_RESOURCES: CrisisResourceItem[] = [
  {
    id: 'cr1',
    name: 'National Suicide Prevention Lifeline',
    contact: '988',
    description: '24/7, free and confidential support for people in distress, prevention and crisis resources.',
    type: 'phone',
  },
  {
    id: 'cr2',
    name: 'Crisis Text Line',
    contact: 'Text HOME to 741741',
    description: 'Free, 24/7 crisis support via text message.',
    type: 'phone',
  },
   {
    id: 'cr4',
    name: 'NAMI Helpline',
    contact: 'https://www.nami.org/help',
    description: 'National Alliance on Mental Illness provides advocacy, education, support and public awareness.',
    type: 'link',
  },
];

export const PLACEHOLDER_PROGRESS_DATA = {
  sessionsCompleted: 3,
  exercisesDone: 5,
  moodAverage: 7, // on a scale of 1-10 (placeholder)
  journalStreak: 12, // days
};

export const PLACEHOLDER_SPACES: Space[] = [
  {
    id: 'space1',
    name: 'Mindful Mornings with Sarah',
    description: 'Start your day with intention and calm. Join for daily meditations, affirmations, and mindful discussions.',
    bannerImageUrl: 'https://picsum.photos/seed/spacebanner1/800/200',
    profileImageUrl: 'https://picsum.photos/seed/spaceprofile1/100/100',
    aiBannerHint: 'serene sunrise',
    aiProfileHint: 'yoga instructor',
    tags: ['Mindfulness', 'Meditation', 'Morning Routine'],
    host: {
      name: 'Sarah K.',
      avatarUrl: 'https://picsum.photos/seed/hostsarah/50/50',
      aiHostAvatarHint: 'friendly woman',
    },
    isSubscriptionBased: false,
    followerCount: 1250,
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
  },
  {
    id: 'space2',
    name: 'The Anxiety Toolkit by Dr. Ben',
    description: 'Evidence-based strategies and support for managing anxiety. Weekly Q&A, CBT exercises, and resource sharing.',
    bannerImageUrl: 'https://picsum.photos/seed/spacebanner2/800/200',
    profileImageUrl: 'https://picsum.photos/seed/spaceprofile2/100/100',
    aiBannerHint: 'calm abstract',
    aiProfileHint: 'therapist professional',
    tags: ['Anxiety', 'CBT', 'Mental Health Support', 'Q&A'],
    host: {
      name: 'Dr. Ben Carter',
      avatarUrl: 'https://picsum.photos/seed/hostben/50/50',
      aiHostAvatarHint: 'doctor man',
    },
    isSubscriptionBased: true,
    subscriptionTiers: [
      { id: 'tier1', name: 'Supporter', pricePerMonth: 500, features: ['Access to all content', 'Monthly live Q&A'] },
      { id: 'tier2', name: 'Premium', pricePerMonth: 1000, features: ['All Supporter benefits', 'Bi-weekly group coaching', 'Early access to new resources'] }
    ],
    followerCount: 780,
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days ago
  },
  {
    id: 'space3',
    name: 'Creative Wellness Collective',
    description: 'Explore your creativity as a path to well-being. Art prompts, journaling challenges, and a supportive community.',
    bannerImageUrl: 'https://picsum.photos/seed/spacebanner3/800/200',
    profileImageUrl: 'https://picsum.photos/seed/spaceprofile3/100/100',
    aiBannerHint: 'art supplies',
    aiProfileHint: 'artist smiling',
    tags: ['Creativity', 'Art Therapy', 'Journaling', 'Community'],
    host: {
      name: 'Alex Chen',
      avatarUrl: 'https://picsum.photos/seed/hostalex/50/50',
      aiHostAvatarHint: 'creative person',
    },
    isSubscriptionBased: false,
    followerCount: 2100,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
  },
];

export const PLACEHOLDER_THERAPIST_CLIENTS: PlaceholderClient[] = [
  { id: 'client1', name: 'Alex Johnson', avatarUrl: 'https://picsum.photos/seed/tclient1/40/40', aiAvatarHint: 'client photo', lastSession: '3 days ago', nextSession: 'Next Tuesday, 10:00 AM', status: 'Active' },
  { id: 'client2', name: 'Maria Garcia', avatarUrl: 'https://picsum.photos/seed/tclient2/40/40', aiAvatarHint: 'client photo', lastSession: '1 week ago', nextSession: 'Not Scheduled', status: 'On Hold' },
  { id: 'client3', name: 'Chen Wei', avatarUrl: 'https://picsum.photos/seed/tclient3/40/40', aiAvatarHint: 'client photo', lastSession: 'Yesterday', nextSession: 'Tomorrow, 2:00 PM', status: 'Active' },
];

export const PLACEHOLDER_THERAPIST_APPOINTMENTS: PlaceholderAppointment[] = [
    { id: 'apt1', time: '09:00 AM', clientName: 'Sarah Miller', duration: '50 min', type: 'Follow-up'},
    { id: 'apt2', time: '10:00 AM', clientName: 'Alex Johnson', duration: '50 min', type: 'Follow-up'},
    { id: 'apt3', time: '11:00 AM', clientName: 'Robert Davis (New)', duration: '60 min', type: 'Initial Consultation'},
    { id: 'apt4', time: '01:00 PM', clientName: 'Group Session: Anxiety', duration: '90 min', type: 'Group Session'},
    { id: 'apt5', time: '02:30 PM', clientName: 'Chen Wei', duration: '50 min', type: 'Follow-up'},
];
