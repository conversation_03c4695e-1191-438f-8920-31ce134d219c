
'use client';

import React from 'react';
import { use<PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { useJournal } from '@/contexts/journal-context';
import type { Mood } from '@/lib/types';
import { MOOD_OPTIONS } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import { Feather } from 'lucide-react';

const journalFormSchema = z.object({
  title: z.string().min(1, { message: "Title is required." }).max(100, { message: "Title must be 100 characters or less." }),
  content: z.string().min(1, { message: "Content is required." }),
  mood: z.custom<Mood>((val) => MOOD_OPTIONS.includes(val as Mood), {
    message: "Please select a valid mood.",
  }),
});

type JournalFormValues = z.infer<typeof journalFormSchema>;

export function JournalEntryForm() {
  const { addJournalEntry } = useJournal();
  const { toast } = useToast();
  const { register, handleSubmit, reset, formState: { errors }, control } = useForm<JournalFormValues>({
    resolver: zodResolver(journalFormSchema),
    defaultValues: {
      title: '',
      content: '',
      mood: 'neutral',
    },
  });

  const onSubmit: SubmitHandler<JournalFormValues> = (data) => {
    addJournalEntry(data);
    toast({
      title: "Journal Entry Saved",
      description: "Your thoughts have been safely recorded.",
    });
    reset();
  };

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <Feather className="text-primary" />
          New Journal Entry
        </CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              placeholder="What's on your mind?"
              {...register('title')}
              className={errors.title ? 'border-destructive' : ''}
            />
            {errors.title && <p className="text-sm text-destructive">{errors.title.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              placeholder="Pour your thoughts here..."
              rows={8}
              {...register('content')}
              className={errors.content ? 'border-destructive' : ''}
            />
            {errors.content && <p className="text-sm text-destructive">{errors.content.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="mood">How are you feeling?</Label>
             <Controller
              name="mood"
              control={control}
              render={({ field }) => (
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <SelectTrigger id="mood" className={errors.mood ? 'border-destructive' : ''}>
                    <SelectValue placeholder="Select your mood" />
                  </SelectTrigger>
                  <SelectContent>
                    {MOOD_OPTIONS.map((mood) => (
                      <SelectItem key={mood} value={mood} className="capitalize">
                        {mood.charAt(0).toUpperCase() + mood.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.mood && <p className="text-sm text-destructive">{errors.mood.message}</p>}
          </div>
        </CardContent>
        <CardFooter>
          <Button type="submit" className="w-full">
            Save Entry
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}

