
'use client';

import React from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { FolderKanban, Download, FileText, Video, BookOpen, Headphones, PlusCircle } from 'lucide-react';
import type { SpaceContent } from '@/lib/types'; // Assuming SpaceContent type exists
import { Badge } from '@/components/ui/badge';

// Placeholder content data
const placeholderContent: SpaceContent[] = [
  {
    id: 'content1',
    spaceId: 'space1',
    title: 'Beginner\'s Guide to Mindfulness (PDF)',
    type: 'pdf',
    description: 'A comprehensive guide to get you started with mindfulness practices.',
    url: '#',
    uploadDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    category: 'Guides'
  },
  {
    id: 'content2',
    spaceId: 'space1',
    title: 'Guided Meditation for Stress Relief (Audio)',
    type: 'meditation',
    description: 'A 15-minute audio meditation to help you unwind and de-stress.',
    url: '#',
    uploadDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    category: 'Meditations'
  },
  {
    id: 'content3',
    spaceId: 'space1',
    title: 'Understanding Your Emotions (Video)',
    type: 'video',
    description: 'An insightful video explaining the basics of emotional intelligence.',
    url: '#',
    uploadDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    category: 'Videos'
  },
];

const getContentTypeIcon = (type: SpaceContent['type']) => {
  switch(type) {
    case 'pdf': return <FileText className="h-4 w-4" />;
    case 'video': return <Video className="h-4 w-4" />;
    case 'guide': return <BookOpen className="h-4 w-4" />;
    case 'meditation': return <Headphones className="h-4 w-4" />;
    case 'article': return <FileText className="h-4 w-4" />;
    default: return <FileText className="h-4 w-4" />;
  }
}

export function ContentSharingSection({ spaceId }: { spaceId: string }) {
  // In a real app, fetch content for the given spaceId
  const contents = placeholderContent.filter(content => content.spaceId === spaceId || placeholderContent);

  return (
    <Card className="shadow-md">
      <CardHeader className="flex flex-row justify-between items-center">
        <div>
            <CardTitle className="flex items-center text-2xl">
            <FolderKanban className="mr-2 h-6 w-6 text-primary" />
            Shared Content & Resources
            </CardTitle>
            <CardDescription>
            Access guides, videos, meditations, and other resources shared in this space.
            </CardDescription>
        </div>
        <Button variant="outline" size="sm"><PlusCircle className="mr-2 h-4 w-4" /> Upload Content</Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {contents.length > 0 ? (
          contents.map(content => (
            <Card key={content.id} className="p-4 bg-muted/50">
              <div className="flex flex-col sm:flex-row justify-between items-start gap-3">
                <div className="flex-grow">
                  <div className="flex items-center gap-2 mb-1">
                    {getContentTypeIcon(content.type)}
                    <h3 className="font-semibold text-md">{content.title}</h3>
                  </div>
                  {content.description && <p className="text-xs text-muted-foreground mb-1 line-clamp-2">{content.description}</p>}
                  <div className="text-xs text-muted-foreground">
                    {content.category && <Badge variant="outline" className="mr-2 capitalize text-xs">{content.category}</Badge>}
                    Uploaded: {new Date(content.uploadDate).toLocaleDateString()}
                  </div>
                </div>
                <Button size="sm" variant="ghost" asChild className="mt-2 sm:mt-0">
                  <a href={content.url} target="_blank" rel="noopener noreferrer">
                    <Download className="mr-2 h-4 w-4" /> Access
                  </a>
                </Button>
              </div>
            </Card>
          ))
        ) : (
          <p className="text-muted-foreground text-center py-4">No content has been shared in this space yet.</p>
        )}
      </CardContent>
      <CardFooter>
        <p className="text-xs text-muted-foreground">
          Content sharing allows hosts to upload PDFs, videos, guides, etc. Resources can be organized by categories or tags.
        </p>
      </CardFooter>
    </Card>
  );
}
