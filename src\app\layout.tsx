import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { JournalProvider } from '@/contexts/journal-context';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'WELL - Your Mental Wellness Companion',
  description: 'WELL - A comprehensive web application for mental wellness, journaling, resources, and community support.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <JournalProvider>
          {children}
          <Toaster />
        </JournalProvider>
      </body>
    </html>
  );
}


