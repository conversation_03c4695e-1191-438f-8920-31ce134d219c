
import { JournalEntryForm } from '@/components/journal/journal-entry-form';
import { JournalList } from '@/components/journal/journal-list';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

export default function JournalPage() {
  return (
    <div className="container mx-auto max-w-4xl py-8"> {/* Simplified layout, removed flex-col and h-full */}
      <h1 className="text-3xl font-bold mb-8 text-center text-primary">My Personal Journal</h1>
      <div className="mb-12"> {/* Added margin bottom for spacing */}
        <JournalEntryForm />
      </div>
      <Separator className="my-12" />
      <h2 className="text-2xl font-semibold mb-6 text-center">Past Entries</h2>
      {/* ScrollArea with a max-height for the list. Page will scroll if form + list > viewport */}
      <ScrollArea className="max-h-[60vh] pr-3"> {/* Adjust max-height as needed, pr-3 for scrollbar spacing */}
         <JournalList />
      </ScrollArea>
    </div>
  );
}
