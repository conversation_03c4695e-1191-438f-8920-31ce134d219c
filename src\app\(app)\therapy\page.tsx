
'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { HeartHandshake } from 'lucide-react';
import { THERAPY_TAB_LINKS } from '@/lib/constants';

import { ConnectTherapists } from '@/components/therapy/connect-therapists';
import { GuidedSessions } from '@/components/therapy/guided-sessions';
import { CbtExercises } from '@/components/therapy/cbt-exercises';
import { CrisisResources } from '@/components/therapy/crisis-resources';
import { ProgressTracker } from '@/components/therapy/progress-tracker';

export default function TherapyPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col items-center justify-center mb-10 space-y-3">
        <HeartHandshake className="h-16 w-16 text-primary" />
        <h1 className="text-4xl font-bold text-center text-primary">Therapy & Support</h1>
        <p className="text-center text-muted-foreground max-w-2xl">
          Access professional support, guided practices, and tools to enhance your mental well-being.
        </p>
      </div>

      <Tabs defaultValue={THERAPY_TAB_LINKS[0].value} className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-5 mb-8">
          {THERAPY_TAB_LINKS.map((link) => (
            <TabsTrigger key={link.value} value={link.value} className="flex items-center gap-2">
              <link.icon className="h-5 w-5" />
              <span>{link.label}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="connect">
          <ConnectTherapists />
        </TabsContent>
        <TabsContent value="sessions">
          <GuidedSessions />
        </TabsContent>
        <TabsContent value="cbt">
          <CbtExercises />
        </TabsContent>
        <TabsContent value="crisis">
          <CrisisResources />
        </TabsContent>
        <TabsContent value="progress">
          <ProgressTracker />
        </TabsContent>
      </Tabs>
    </div>
  );
}
