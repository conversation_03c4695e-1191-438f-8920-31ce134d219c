{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9002", "dev:backend": "cd backend && npx ts-node --transpile-only server.ts", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build && tsc --project backend/tsconfig.json", "start": "next start", "start:backend": "node backend/dist/server.js", "lint": "next lint", "typecheck": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node backend/prisma/seed.ts"}, "dependencies": {"@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.1.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "firebase": "^11.7.0", "genkit": "^1.8.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "mysql2": "^3.11.4", "next": "15.2.3", "nodemailer": "^6.9.18", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "stripe": "^17.5.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "zod": "^3.24.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/nodemailer": "^6.4.15", "@types/react": "^18", "@types/react-dom": "^18", "@types/speakeasy": "^2.0.10", "@types/uuid": "^10.0.0", "genkit-cli": "^1.8.0", "nodemon": "^3.1.9", "postcss": "^8", "prisma": "^6.1.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}