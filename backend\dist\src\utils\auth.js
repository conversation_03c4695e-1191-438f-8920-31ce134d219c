"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthUtils = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const crypto_1 = __importDefault(require("crypto"));
const speakeasy_1 = __importDefault(require("speakeasy"));
const client_1 = require("@prisma/client");
class AuthUtils {
    static async hashPassword(password) {
        const saltRounds = 12;
        return bcryptjs_1.default.hash(password, saltRounds);
    }
    static async comparePassword(password, hashedPassword) {
        return bcryptjs_1.default.compare(password, hashedPassword);
    }
    static generateAccessToken(payload) {
        if (!process.env.JWT_SECRET) {
            throw new Error('JWT_SECRET not configured');
        }
        return jsonwebtoken_1.default.sign(payload, process.env.JWT_SECRET, {
            expiresIn: process.env.JWT_EXPIRES_IN || '15m',
            issuer: 'well-platform',
            audience: 'well-users',
        });
    }
    static generateRefreshToken(payload) {
        if (!process.env.JWT_REFRESH_SECRET) {
            throw new Error('JWT_REFRESH_SECRET not configured');
        }
        return jsonwebtoken_1.default.sign(payload, process.env.JWT_REFRESH_SECRET, {
            expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
            issuer: 'well-platform',
            audience: 'well-users',
        });
    }
    static verifyRefreshToken(token) {
        if (!process.env.JWT_REFRESH_SECRET) {
            throw new Error('JWT_REFRESH_SECRET not configured');
        }
        return jsonwebtoken_1.default.verify(token, process.env.JWT_REFRESH_SECRET);
    }
    static generateTokenPair(user) {
        const payload = {
            userId: user.id,
            email: user.email,
            role: user.role,
        };
        return {
            accessToken: this.generateAccessToken(payload),
            refreshToken: this.generateRefreshToken(payload),
        };
    }
    static generateEmailVerificationToken() {
        return crypto_1.default.randomBytes(32).toString('hex');
    }
    static generatePasswordResetToken() {
        return crypto_1.default.randomBytes(32).toString('hex');
    }
    static generateTwoFactorSecret() {
        const secret = speakeasy_1.default.generateSecret({
            name: process.env.TWO_FACTOR_SERVICE_NAME || 'WELL Platform',
            length: 32,
        });
        return {
            secret: secret.base32,
            qrCodeUrl: secret.otpauth_url || '',
        };
    }
    static verifyTwoFactorToken(token, secret) {
        return speakeasy_1.default.totp.verify({
            secret,
            encoding: 'base32',
            token,
            window: 2,
        });
    }
    static validatePassword(password) {
        const errors = [];
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    static generateSessionId() {
        return crypto_1.default.randomUUID();
    }
    static generateJitsiRoomId(sessionId) {
        const timestamp = Date.now();
        const random = crypto_1.default.randomBytes(4).toString('hex');
        return `well-session-${sessionId}-${timestamp}-${random}`;
    }
    static sanitizeUserData(user) {
        return {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            emailVerified: user.emailVerified,
            twoFactorEnabled: user.twoFactorEnabled,
        };
    }
    static createAuthResponse(user) {
        const tokens = this.generateTokenPair(user);
        return {
            user: this.sanitizeUserData(user),
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
        };
    }
    static isValidRole(role) {
        return Object.values(client_1.UserRole).includes(role);
    }
    static canAccessResource(userRole, requiredRoles) {
        return requiredRoles.includes(userRole);
    }
    static getRoleHierarchy() {
        return {
            [client_1.UserRole.PATIENT]: 1,
            [client_1.UserRole.PSYCHIATRIST]: 2,
            [client_1.UserRole.MODERATOR]: 3,
            [client_1.UserRole.ADMIN]: 4,
        };
    }
    static hasHigherRole(userRole, targetRole) {
        const hierarchy = this.getRoleHierarchy();
        return hierarchy[userRole] > hierarchy[targetRole];
    }
    static generateApiKey() {
        return crypto_1.default.randomBytes(32).toString('hex');
    }
    static hashApiKey(apiKey) {
        return crypto_1.default.createHash('sha256').update(apiKey).digest('hex');
    }
}
exports.AuthUtils = AuthUtils;
//# sourceMappingURL=auth.js.map