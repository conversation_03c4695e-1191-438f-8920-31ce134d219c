
'use client';

import React, { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useJournal } from '@/contexts/journal-context';
import { UserCircle2, Edit3, Shield, Palette, Bell, BarChart3, Settings, Sun, Moon, LogOut } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function ProfilePage() {
  const { journalEntries } = useJournal();
  const { toast } = useToast();
  const [mounted, setMounted] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Check initial theme preference
    const currentTheme = document.documentElement.classList.contains('dark');
    setIsDarkMode(currentTheme);
  }, []);

  const toggleTheme = () => {
    setIsDarkMode(prevMode => {
      const newMode = !prevMode;
      if (newMode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
      localStorage.setItem('theme', newMode ? 'dark' : 'light'); // Persist theme preference
      return newMode;
    });
     toast({
      title: "Theme Changed",
      description: `Switched to ${!isDarkMode ? "Dark" : "Light"} Mode.`,
    });
  };
  
  const handlePlaceholderAction = (actionName: string) => {
    toast({
      title: "Action Triggered (Placeholder)",
      description: `${actionName} functionality is not yet implemented.`,
    });
  };

  if (!mounted) {
    return null; // Or a loading skeleton
  }

  return (
    <div className="container mx-auto py-8 max-w-4xl space-y-12">
      <div className="flex flex-col items-center justify-center mb-10 space-y-3">
        <UserCircle2 className="h-16 w-16 text-primary" />
        <h1 className="text-4xl font-bold text-center text-primary">My Profile</h1>
        <p className="text-center text-muted-foreground">Manage your account settings and preferences.</p>
      </div>

      {/* User Information Card */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl">Personal Information</CardTitle>
          <CardDescription>Your basic profile details.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center space-x-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src="https://picsum.photos/seed/profile-avatar/200/200" alt="User Avatar" data-ai-hint="profile avatar" />
              <AvatarFallback>WU</AvatarFallback> {/* WU for WELL User */}
            </Avatar>
            <div>
              <h3 className="text-xl font-semibold">WELL User</h3>
              <p className="text-muted-foreground"><EMAIL></p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input id="name" value="WELL User" readOnly />
            </div>
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input id="email" type="email" value="<EMAIL>" readOnly />
            </div>
            <div>
              <Label htmlFor="joinDate">Joined Date</Label>
              <Input id="joinDate" value={new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })} readOnly />
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onClick={() => handlePlaceholderAction("Edit Profile")}>
            <Edit3 className="mr-2 h-4 w-4" /> Edit Profile
          </Button>
        </CardFooter>
      </Card>

      {/* App Statistics Card */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <BarChart3 className="mr-2 h-6 w-6 text-primary" />
            Your Activity
          </CardTitle>
          <CardDescription>An overview of your engagement with WELL.</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <div className="p-4 bg-muted/50 rounded-lg text-center">
            <p className="text-3xl font-bold text-primary">{journalEntries.length}</p>
            <p className="text-sm text-muted-foreground">Journal Entries</p>
          </div>
          <div className="p-4 bg-muted/50 rounded-lg text-center">
            <p className="text-3xl font-bold text-primary">N/A</p>
            <p className="text-sm text-muted-foreground">Insights Unlocked</p>
          </div>
        </CardContent>
      </Card>
      
      {/* Account Settings Card */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <Settings className="mr-2 h-6 w-6 text-primary" />
            Account Settings
          </CardTitle>
          <CardDescription>Manage your account details and security.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button variant="outline" className="w-full justify-start" onClick={() => handlePlaceholderAction("Change Password")}>
            <Shield className="mr-2 h-4 w-4" /> Change Password
          </Button>
          <Button variant="outline" className="w-full justify-start" onClick={() => handlePlaceholderAction("Manage Subscription")}>
            {/* Placeholder for subscription icon */}
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4"><path d="M20 12V8H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h12v4"/><path d="M4 6v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-4"/><path d="M18 18h-3a2 2 0 0 1-2-2v0a2 2 0 0 1 2-2h3v4Z"/></svg>
            Manage Subscription (Placeholder)
          </Button>
          <Button variant="destructive" className="w-full justify-start" onClick={() => handlePlaceholderAction("Delete Account")}>
             <LogOut className="mr-2 h-4 w-4" /> Delete Account (Placeholder)
          </Button>
        </CardContent>
      </Card>

      {/* Preferences Card */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <Palette className="mr-2 h-6 w-6 text-primary" />
             Preferences
          </CardTitle>
          <CardDescription>Customize your WELL experience.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
           <div className="flex items-center justify-between p-3 border rounded-md">
            <div className="flex items-center">
              {isDarkMode ? <Moon className="mr-2 h-5 w-5" /> : <Sun className="mr-2 h-5 w-5" />}
              <span>Interface Theme</span>
            </div>
            <Button variant="outline" size="sm" onClick={toggleTheme}>
              Switch to {isDarkMode ? 'Light' : 'Dark'} Mode
            </Button>
          </div>
          <Button variant="outline" className="w-full justify-start" onClick={() => handlePlaceholderAction("Notification Settings")}>
            <Bell className="mr-2 h-4 w-4" /> Notification Settings
          </Button>
          <Button variant="outline" className="w-full justify-start" onClick={() => handlePlaceholderAction("Data & Privacy Settings")}>
             {/* Placeholder for data privacy icon */}
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 h-4 w-4"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"/><path d="M7 11V7a5 5 0 0 1 10 0v4"/></svg>
            Data & Privacy
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

