'use client';

import React from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Send } from 'lucide-react';

const newPostSchema = z.object({
  title: z.string().min(1, "Title is required.").max(150, "Title must be 150 characters or less."),
  content: z.string().min(1, "Content is required."),
});

type NewPostFormValues = z.infer<typeof newPostSchema>;

interface NewPostFormProps {
  onAddPost: (post: Omit<NewPostFormValues, 'id' | 'author' | 'date'>) => void;
}

export function NewPostForm({ onAddPost }: NewPostFormProps) {
  const { toast } = useToast();
  const { register, handleSubmit, reset, formState: { errors } } = useForm<NewPostFormValues>({
    resolver: zodResolver(newPostSchema),
  });

  const onSubmit: SubmitHandler<NewPostFormValues> = (data) => {
    onAddPost(data);
    toast({
      title: "Post Created",
      description: "Your post has been added to the community.",
    });
    reset();
  };

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-lg mb-12">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <Send className="text-primary" />
          Create New Post
        </CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="postTitle">Title</Label>
            <Input
              id="postTitle"
              placeholder="What's the topic of your post?"
              {...register('title')}
              className={errors.title ? 'border-destructive' : ''}
            />
            {errors.title && <p className="text-sm text-destructive">{errors.title.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="postContent">Content</Label>
            <Textarea
              id="postContent"
              placeholder="Share your thoughts with the community..."
              rows={6}
              {...register('content')}
              className={errors.content ? 'border-destructive' : ''}
            />
            {errors.content && <p className="text-sm text-destructive">{errors.content.message}</p>}
          </div>
        </CardContent>
        <CardFooter>
          <Button type="submit" className="w-full">
            Share Post
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
