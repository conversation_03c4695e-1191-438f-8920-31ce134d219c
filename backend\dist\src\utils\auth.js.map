{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/utils/auth.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAA+B;AAC/B,wDAA8B;AAC9B,oDAA4B;AAC5B,0DAAkC;AAClC,2CAA0C;AAG1C,MAAa,SAAS;IACpB,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAgB;QACxC,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,OAAO,kBAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,cAAsB;QACnE,OAAO,kBAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,OAAmB;QAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,sBAAG,CAAC,IAAI,CACb,OAAO,EACP,OAAO,CAAC,GAAG,CAAC,UAAU,EACtB;YACE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK;YAC9C,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,YAAY;SACvB,CACF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,OAAmB;QAC7C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,sBAAG,CAAC,IAAI,CACb,OAAO,EACP,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAC9B;YACE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI;YACrD,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,YAAY;SACvB,CACF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,KAAa;QACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAe,CAAC;IACzE,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,IAAuB;QAC9C,MAAM,OAAO,GAAe;YAC1B,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;QAEF,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YAC9C,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;SACjD,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,8BAA8B;QACnC,OAAO,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,CAAC,0BAA0B;QAC/B,OAAO,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,CAAC,uBAAuB;QAC5B,MAAM,MAAM,GAAG,mBAAS,CAAC,cAAc,CAAC;YACtC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,eAAe;YAC5D,MAAM,EAAE,EAAE;SACX,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,SAAS,EAAE,MAAM,CAAC,WAAW,IAAI,EAAE;SACpC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,KAAa,EAAE,MAAc;QACvD,OAAO,mBAAS,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3B,MAAM;YACN,QAAQ,EAAE,QAAQ;YAClB,KAAK;YACL,MAAM,EAAE,CAAC;SACV,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QACtC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,KAAa;QAChC,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,iBAAiB;QACtB,OAAO,gBAAM,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,SAAiB;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,gBAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrD,OAAO,gBAAgB,SAAS,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;IAC5D,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,IAAS;QAC/B,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;SACxC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,IAAuB;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE5C,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACjC,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,YAAY,EAAE,MAAM,CAAC,YAAY;SAClC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,IAAY;QAC7B,OAAO,MAAM,CAAC,MAAM,CAAC,iBAAQ,CAAC,CAAC,QAAQ,CAAC,IAAgB,CAAC,CAAC;IAC5D,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,QAAkB,EAAE,aAAyB;QACpE,OAAO,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,gBAAgB;QACrB,OAAO;YACL,CAAC,iBAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrB,CAAC,iBAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC1B,CAAC,iBAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvB,CAAC,iBAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;SACpB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,QAAkB,EAAE,UAAoB;QAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1C,OAAO,SAAS,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,cAAc;QACnB,OAAO,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,MAAc;QAC9B,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAClE,CAAC;CACF;AA5LD,8BA4LC"}