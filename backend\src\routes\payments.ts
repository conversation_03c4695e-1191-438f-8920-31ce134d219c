import { Router } from 'express';
import { z } from 'zod';
import { UserRole } from '@prisma/client';
import { prisma } from '../../server';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { requireRole, requireEmailVerification } from '../middleware/auth';

const router = Router();

// Placeholder for payment routes
// TODO: Implement Stripe integration

// Create payment intent
router.post('/create-intent', requireEmailVerification, asyncHandler(async (req, res) => {
  // TODO: Implement Stripe payment intent creation
  res.json({
    success: true,
    message: 'Payment routes not yet implemented',
    data: { clientSecret: 'placeholder' },
  });
}));

// Get payment history
router.get('/', requireEmailVerification, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  
  const payments = await prisma.payment.findMany({
    where: { userId },
    include: {
      session: {
        include: {
          psychiatrist: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });

  res.json({
    success: true,
    data: payments,
  });
}));

export default router;
