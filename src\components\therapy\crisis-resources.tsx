
'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { PLACEHOLDER_CRISIS_RESOURCES } from '@/lib/constants';
import type { CrisisResourceItem } from '@/lib/types';
import { PhoneCall, ExternalLink, ShieldAlert } from 'lucide-react';

export function CrisisResources() {
  return (
    <div className="space-y-8">
      <Alert variant="destructive" className="shadow-lg">
        <ShieldAlert className="h-5 w-5" />
        <AlertTitle className="text-xl">Immediate Support Needed?</AlertTitle>
        <AlertDescription>
          If you are in a crisis or any other person may be in danger, please don&apos;t use this site. 
          These resources can provide immediate help.
        </AlertDescription>
      </Alert>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl flex items-center gap-2"><PhoneCall className="h-7 w-7 text-primary"/>Emergency Contact Information</CardTitle>
          <CardDescription>
            Quick access to helplines and resources for urgent mental health support.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {PLACEHOLDER_CRISIS_RESOURCES.map((resource) => (
            <Card key={resource.id} className="p-4 bg-muted/50">
              <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3">
                <div>
                  <h3 className="font-semibold text-lg text-primary">{resource.name}</h3>
                  <p className="text-sm text-muted-foreground">{resource.description}</p>
                </div>
                <Button 
                  asChild 
                  variant={resource.type === 'phone' ? "default" : "outline"} 
                  className="w-full sm:w-auto"
                >
                  <a 
                    href={resource.type === 'phone' ? `tel:${resource.contact}` : resource.contact}
                    target={resource.type === 'link' ? "_blank" : undefined}
                    rel={resource.type === 'link' ? "noopener noreferrer" : undefined}
                  >
                    {resource.type === 'phone' ? <PhoneCall className="mr-2 h-4 w-4" /> : <ExternalLink className="mr-2 h-4 w-4" />}
                    {resource.contact}
                  </a>
                </Button>
              </div>
            </Card>
          ))}
        </CardContent>
      </Card>
      <p className="text-center text-sm text-muted-foreground">
        Remember, reaching out is a sign of strength. You are not alone.
      </p>
    </div>
  );
}
