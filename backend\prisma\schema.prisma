// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  PATIENT
  PSYCHIATRIST
  MODERATOR
  ADMIN
}

enum UserStatus {
  PENDING
  ACTIVE
  SUSPENDED
  BANNED
  PENDING_APPROVAL
}

enum SessionStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum PaymentType {
  SESSION
  SUBSCRIPTION
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  SYSTEM
}

enum PostStatus {
  ACTIVE
  HIDDEN
  DELETED
  FLAGGED
}

model User {
  id                String      @id @default(cuid())
  email             String      @unique
  password          String
  firstName         String
  lastName          String
  role              UserRole    @default(PATIENT)
  status            UserStatus  @default(PENDING)
  emailVerified     <PERSON>olean     @default(false)
  emailVerificationToken String?
  passwordResetToken String?
  passwordResetExpires DateTime?
  twoFactorEnabled  Boolean     @default(false)
  twoFactorSecret   String?
  profileImage      String?
  phoneNumber       String?
  dateOfBirth       DateTime?
  timezone          String      @default("UTC")
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  lastLoginAt       DateTime?

  // Patient specific fields
  patientProfile    PatientProfile?
  
  // Psychiatrist specific fields
  psychiatristProfile PsychiatristProfile?
  
  // Relationships
  sentMessages      Message[]   @relation("MessageSender")
  receivedMessages  Message[]   @relation("MessageReceiver")
  patientSessions   Session[]   @relation("SessionPatient")
  psychiatristSessions Session[] @relation("SessionPsychiatrist")
  payments          Payment[]
  subscriptions     Subscription[]
  forumPosts        ForumPost[]
  forumComments     ForumComment[]
  reviews           Review[]    @relation("ReviewAuthor")
  receivedReviews   Review[]    @relation("ReviewTarget")
  moderationActions ModerationAction[]
  aiInteractions    AIInteraction[]
  emergencyContacts EmergencyContact[]
  
  @@map("users")
}

model PatientProfile {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  emergencyContact  String?
  medicalHistory    String?  @db.Text
  currentMedications String? @db.Text
  allergies         String?  @db.Text
  preferredLanguages String?
  insuranceInfo     String?  @db.Text
  consentToAI       Boolean  @default(false)
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@map("patient_profiles")
}

model PsychiatristProfile {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  licenseNumber     String   @unique
  specialties       String   @db.Text // JSON array of specialties
  bio               String?  @db.Text
  education         String?  @db.Text
  experience        String?  @db.Text
  languages         String?  @db.Text // JSON array of languages
  hourlyRate        Decimal  @db.Decimal(10, 2)
  acceptsInsurance  Boolean  @default(false)
  verified          Boolean  @default(false)
  verificationDocs  String?  @db.Text // JSON array of document URLs
  
  // Availability
  availability      Availability[]
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@map("psychiatrist_profiles")
}

model Availability {
  id              String   @id @default(cuid())
  psychiatristId  String
  psychiatrist    PsychiatristProfile @relation(fields: [psychiatristId], references: [id], onDelete: Cascade)
  
  dayOfWeek       Int      // 0 = Sunday, 1 = Monday, etc.
  startTime       String   // HH:MM format
  endTime         String   // HH:MM format
  isAvailable     Boolean  @default(true)
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([psychiatristId, dayOfWeek, startTime])
  @@map("availability")
}

model Session {
  id              String        @id @default(cuid())
  patientId       String
  psychiatristId  String
  patient         User          @relation("SessionPatient", fields: [patientId], references: [id])
  psychiatrist    User          @relation("SessionPsychiatrist", fields: [psychiatristId], references: [id])

  scheduledAt     DateTime
  duration        Int           // Duration in minutes
  status          SessionStatus @default(SCHEDULED)
  jitsiRoomId     String?       @unique
  sessionNotes    String?       @db.Text
  patientNotes    String?       @db.Text

  // Payment
  paymentId       String?       @unique
  payment         Payment?      @relation(fields: [paymentId], references: [id])

  // Messages during session
  messages        Message[]

  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  startedAt       DateTime?
  endedAt         DateTime?

  @@map("sessions")
}

model Message {
  id              String      @id @default(cuid())
  senderId        String
  receiverId      String?     // Null for group messages
  sessionId       String?     // If message is part of a session

  sender          User        @relation("MessageSender", fields: [senderId], references: [id])
  receiver        User?       @relation("MessageReceiver", fields: [receiverId], references: [id])
  session         Session?    @relation(fields: [sessionId], references: [id])

  content         String      @db.Text
  messageType     MessageType @default(TEXT)
  fileUrl         String?
  fileName        String?
  fileSize        Int?

  isRead          Boolean     @default(false)
  readAt          DateTime?
  isEdited        Boolean     @default(false)
  editedAt        DateTime?

  // Moderation
  isFlagged       Boolean     @default(false)
  flaggedReason   String?
  moderatedBy     String?
  moderatedAt     DateTime?

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("messages")
}

model Payment {
  id              String        @id @default(cuid())
  userId          String
  user            User          @relation(fields: [userId], references: [id])

  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  paymentType     PaymentType
  status          PaymentStatus @default(PENDING)

  // Stripe integration
  stripePaymentIntentId String? @unique
  stripeChargeId      String?   @unique

  // Session payment
  session         Session?

  // Subscription payment
  subscriptionId  String?
  subscription    Subscription? @relation(fields: [subscriptionId], references: [id])

  description     String?
  metadata        String?       @db.Text // JSON for additional data

  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("payments")
}

model Subscription {
  id              String        @id @default(cuid())
  userId          String
  user            User          @relation(fields: [userId], references: [id])

  planName        String
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  billingCycle    String        // monthly, yearly

  // Stripe integration
  stripeSubscriptionId String?  @unique
  stripeCustomerId    String?

  status          String        @default("active") // active, cancelled, past_due
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  cancelAtPeriodEnd  Boolean   @default(false)

  payments        Payment[]

  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("subscriptions")
}

model ForumPost {
  id              String      @id @default(cuid())
  authorId        String
  author          User        @relation(fields: [authorId], references: [id])

  title           String
  content         String      @db.Text
  category        String
  tags            String?     @db.Text // JSON array
  isAnonymous     Boolean     @default(false)
  status          PostStatus  @default(ACTIVE)

  // AI Analysis
  aiAnalyzed      Boolean     @default(false)
  aiSentiment     String?     // positive, negative, neutral, concerning
  aiRiskLevel     String?     // low, medium, high, critical
  aiSuggestions   String?     @db.Text // JSON array of AI suggestions

  // Moderation
  isFlagged       Boolean     @default(false)
  flaggedReason   String?
  moderatedBy     String?
  moderatedAt     DateTime?

  // Engagement
  viewCount       Int         @default(0)
  likeCount       Int         @default(0)

  comments        ForumComment[]

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("forum_posts")
}

model ForumComment {
  id              String      @id @default(cuid())
  postId          String
  authorId        String
  parentId        String?     // For nested comments

  post            ForumPost   @relation(fields: [postId], references: [id], onDelete: Cascade)
  author          User        @relation(fields: [authorId], references: [id])
  parent          ForumComment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies         ForumComment[] @relation("CommentReplies")

  content         String      @db.Text
  isAnonymous     Boolean     @default(false)
  status          PostStatus  @default(ACTIVE)

  // Moderation
  isFlagged       Boolean     @default(false)
  flaggedReason   String?
  moderatedBy     String?
  moderatedAt     DateTime?

  // Engagement
  likeCount       Int         @default(0)

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("forum_comments")
}

model Review {
  id              String      @id @default(cuid())
  authorId        String
  targetId        String      // Psychiatrist being reviewed
  sessionId       String?     // Optional: link to specific session

  author          User        @relation("ReviewAuthor", fields: [authorId], references: [id])
  target          User        @relation("ReviewTarget", fields: [targetId], references: [id])

  rating          Int         // 1-5 stars
  title           String?
  content         String?     @db.Text
  isAnonymous     Boolean     @default(false)

  // Moderation
  isApproved      Boolean     @default(false)
  moderatedBy     String?
  moderatedAt     DateTime?

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@unique([authorId, targetId, sessionId])
  @@map("reviews")
}

model ModerationAction {
  id              String      @id @default(cuid())
  moderatorId     String
  targetUserId    String?
  targetPostId    String?
  targetMessageId String?

  moderator       User        @relation(fields: [moderatorId], references: [id])

  action          String      // suspend, ban, hide_post, delete_message, etc.
  reason          String      @db.Text
  duration        Int?        // Duration in hours for temporary actions
  notes           String?     @db.Text

  createdAt       DateTime    @default(now())

  @@map("moderation_actions")
}

model AIInteraction {
  id              String      @id @default(cuid())
  userId          String
  user            User        @relation(fields: [userId], references: [id])

  interactionType String      // journal_analysis, forum_suggestion, crisis_detection
  inputData       String      @db.Text // JSON of input data
  outputData      String      @db.Text // JSON of AI response

  // Metadata
  model           String      // AI model used
  confidence      Float?      // Confidence score
  processingTime  Int?        // Processing time in ms

  // User feedback
  userFeedback    String?     // helpful, not_helpful, inappropriate
  feedbackNotes   String?     @db.Text

  createdAt       DateTime    @default(now())

  @@map("ai_interactions")
}

model EmergencyContact {
  id              String      @id @default(cuid())
  userId          String
  user            User        @relation(fields: [userId], references: [id])

  name            String
  relationship    String
  phoneNumber     String
  email           String?
  isPrimary       Boolean     @default(false)

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("emergency_contacts")
}

model CrisisResource {
  id              String      @id @default(cuid())

  name            String
  description     String      @db.Text
  phoneNumber     String?
  website         String?
  region          String      // Country or region code
  category        String      // suicide_prevention, crisis_hotline, emergency
  isActive        Boolean     @default(true)

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("crisis_resources")
}

model AuditLog {
  id              String      @id @default(cuid())
  userId          String?     // Null for system actions

  action          String      // login, logout, create_session, payment, etc.
  entityType      String?     // user, session, payment, etc.
  entityId        String?     // ID of the affected entity

  details         String?     @db.Text // JSON with additional details
  ipAddress       String?
  userAgent       String?     @db.Text

  createdAt       DateTime    @default(now())

  @@map("audit_logs")
}

model SystemSetting {
  id              String      @id @default(cuid())
  key             String      @unique
  value           String      @db.Text
  description     String?     @db.Text

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("system_settings")
}
