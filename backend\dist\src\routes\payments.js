"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const server_1 = require("../../server");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.post('/create-intent', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({
        success: true,
        message: 'Payment routes not yet implemented',
        data: { clientSecret: 'placeholder' },
    });
}));
router.get('/', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const payments = await server_1.prisma.payment.findMany({
        where: { userId },
        include: {
            session: {
                include: {
                    psychiatrist: {
                        select: {
                            firstName: true,
                            lastName: true,
                        },
                    },
                },
            },
        },
        orderBy: { createdAt: 'desc' },
    });
    res.json({
        success: true,
        data: payments,
    });
}));
exports.default = router;
//# sourceMappingURL=payments.js.map