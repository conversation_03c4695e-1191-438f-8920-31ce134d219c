"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
const server_1 = require("../../server");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const auth_2 = require("../utils/auth");
const router = (0, express_1.Router)();
const updateProfileSchema = zod_1.z.object({
    firstName: zod_1.z.string().min(1).optional(),
    lastName: zod_1.z.string().min(1).optional(),
    phoneNumber: zod_1.z.string().optional(),
    dateOfBirth: zod_1.z.string().optional(),
    timezone: zod_1.z.string().optional(),
    profileImage: zod_1.z.string().url().optional(),
});
const changePasswordSchema = zod_1.z.object({
    currentPassword: zod_1.z.string().min(1, 'Current password is required'),
    newPassword: zod_1.z.string().min(8, 'New password must be at least 8 characters'),
});
const setupTwoFactorSchema = zod_1.z.object({
    enable: zod_1.z.boolean(),
    token: zod_1.z.string().optional(),
});
router.get('/profile', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await server_1.prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            status: true,
            emailVerified: true,
            twoFactorEnabled: true,
            profileImage: true,
            phoneNumber: true,
            dateOfBirth: true,
            timezone: true,
            createdAt: true,
            lastLoginAt: true,
            patientProfile: {
                select: {
                    emergencyContact: true,
                    medicalHistory: true,
                    currentMedications: true,
                    allergies: true,
                    preferredLanguages: true,
                    consentToAI: true,
                },
            },
            psychiatristProfile: {
                select: {
                    licenseNumber: true,
                    specialties: true,
                    bio: true,
                    education: true,
                    experience: true,
                    languages: true,
                    hourlyRate: true,
                    acceptsInsurance: true,
                    verified: true,
                },
            },
        },
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    const userData = {
        ...user,
        psychiatristProfile: user.psychiatristProfile ? {
            ...user.psychiatristProfile,
            specialties: user.psychiatristProfile.specialties ? JSON.parse(user.psychiatristProfile.specialties) : [],
            education: user.psychiatristProfile.education ? JSON.parse(user.psychiatristProfile.education) : [],
            languages: user.psychiatristProfile.languages ? JSON.parse(user.psychiatristProfile.languages) : [],
        } : null,
    };
    res.json({
        success: true,
        data: userData,
    });
}));
router.put('/profile', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = updateProfileSchema.parse(req.body);
    const updateData = {};
    if (validatedData.firstName)
        updateData.firstName = validatedData.firstName;
    if (validatedData.lastName)
        updateData.lastName = validatedData.lastName;
    if (validatedData.phoneNumber)
        updateData.phoneNumber = validatedData.phoneNumber;
    if (validatedData.timezone)
        updateData.timezone = validatedData.timezone;
    if (validatedData.profileImage)
        updateData.profileImage = validatedData.profileImage;
    if (validatedData.dateOfBirth)
        updateData.dateOfBirth = new Date(validatedData.dateOfBirth);
    const user = await server_1.prisma.user.update({
        where: { id: req.user.id },
        data: updateData,
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            phoneNumber: true,
            dateOfBirth: true,
            timezone: true,
            profileImage: true,
            updatedAt: true,
        },
    });
    res.json({
        success: true,
        message: 'Profile updated successfully',
        data: user,
    });
}));
router.put('/password', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { currentPassword, newPassword } = changePasswordSchema.parse(req.body);
    const passwordValidation = auth_2.AuthUtils.validatePassword(newPassword);
    if (!passwordValidation.isValid) {
        throw new errorHandler_1.CustomError(passwordValidation.errors.join(', '), 400);
    }
    const user = await server_1.prisma.user.findUnique({
        where: { id: req.user.id },
        select: { id: true, password: true },
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    const isCurrentPasswordValid = await auth_2.AuthUtils.comparePassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
        throw new errorHandler_1.CustomError('Current password is incorrect', 400);
    }
    const hashedNewPassword = await auth_2.AuthUtils.hashPassword(newPassword);
    await server_1.prisma.user.update({
        where: { id: req.user.id },
        data: { password: hashedNewPassword },
    });
    res.json({
        success: true,
        message: 'Password changed successfully',
    });
}));
router.post('/two-factor', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { enable, token } = setupTwoFactorSchema.parse(req.body);
    const user = await server_1.prisma.user.findUnique({
        where: { id: req.user.id },
        select: { id: true, twoFactorEnabled: true, twoFactorSecret: true },
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    if (enable) {
        if (user.twoFactorEnabled) {
            throw new errorHandler_1.CustomError('Two-factor authentication is already enabled', 400);
        }
        let secret = user.twoFactorSecret;
        if (!secret) {
            const twoFactorData = auth_2.AuthUtils.generateTwoFactorSecret();
            secret = twoFactorData.secret;
            await server_1.prisma.user.update({
                where: { id: req.user.id },
                data: { twoFactorSecret: secret },
            });
            return res.json({
                success: true,
                message: 'Two-factor authentication secret generated',
                data: {
                    secret,
                    qrCodeUrl: twoFactorData.qrCodeUrl,
                },
            });
        }
        if (!token) {
            throw new errorHandler_1.CustomError('Token is required to enable two-factor authentication', 400);
        }
        const isValidToken = auth_2.AuthUtils.verifyTwoFactorToken(token, secret);
        if (!isValidToken) {
            throw new errorHandler_1.CustomError('Invalid two-factor authentication token', 400);
        }
        await server_1.prisma.user.update({
            where: { id: req.user.id },
            data: { twoFactorEnabled: true },
        });
        res.json({
            success: true,
            message: 'Two-factor authentication enabled successfully',
        });
    }
    else {
        if (!user.twoFactorEnabled) {
            throw new errorHandler_1.CustomError('Two-factor authentication is not enabled', 400);
        }
        if (!token || !user.twoFactorSecret) {
            throw new errorHandler_1.CustomError('Token is required to disable two-factor authentication', 400);
        }
        const isValidToken = auth_2.AuthUtils.verifyTwoFactorToken(token, user.twoFactorSecret);
        if (!isValidToken) {
            throw new errorHandler_1.CustomError('Invalid two-factor authentication token', 400);
        }
        await server_1.prisma.user.update({
            where: { id: req.user.id },
            data: {
                twoFactorEnabled: false,
                twoFactorSecret: null,
            },
        });
        res.json({
            success: true,
            message: 'Two-factor authentication disabled successfully',
        });
    }
}));
router.get('/stats', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const userRole = req.user.role;
    let stats = {};
    if (userRole === client_1.UserRole.PATIENT) {
        const [totalSessions, completedSessions, upcomingSessions, totalMessages] = await Promise.all([
            server_1.prisma.session.count({ where: { patientId: userId } }),
            server_1.prisma.session.count({ where: { patientId: userId, status: 'COMPLETED' } }),
            server_1.prisma.session.count({
                where: {
                    patientId: userId,
                    status: 'SCHEDULED',
                    scheduledAt: { gt: new Date() }
                }
            }),
            server_1.prisma.message.count({ where: { senderId: userId } }),
        ]);
        stats = {
            totalSessions,
            completedSessions,
            upcomingSessions,
            totalMessages,
        };
    }
    else if (userRole === client_1.UserRole.PSYCHIATRIST) {
        const [totalSessions, completedSessions, upcomingSessions, totalPatients, totalEarnings] = await Promise.all([
            server_1.prisma.session.count({ where: { psychiatristId: userId } }),
            server_1.prisma.session.count({ where: { psychiatristId: userId, status: 'COMPLETED' } }),
            server_1.prisma.session.count({
                where: {
                    psychiatristId: userId,
                    status: 'SCHEDULED',
                    scheduledAt: { gt: new Date() }
                }
            }),
            server_1.prisma.session.groupBy({
                by: ['patientId'],
                where: { psychiatristId: userId },
                _count: { patientId: true },
            }).then(result => result.length),
            server_1.prisma.payment.aggregate({
                where: {
                    session: { psychiatristId: userId },
                    status: 'COMPLETED',
                },
                _sum: { amount: true },
            }).then(result => result._sum.amount || 0),
        ]);
        stats = {
            totalSessions,
            completedSessions,
            upcomingSessions,
            totalPatients,
            totalEarnings,
        };
    }
    res.json({
        success: true,
        data: stats,
    });
}));
router.delete('/account', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const activeSessions = await server_1.prisma.session.count({
        where: {
            OR: [
                { patientId: userId },
                { psychiatristId: userId },
            ],
            status: { in: ['SCHEDULED', 'IN_PROGRESS'] },
        },
    });
    if (activeSessions > 0) {
        throw new errorHandler_1.CustomError('Cannot delete account with active sessions. Please complete or cancel all sessions first.', 400);
    }
    await server_1.prisma.user.update({
        where: { id: userId },
        data: {
            status: 'BANNED',
            email: `deleted_${Date.now()}_${req.user.email}`,
        },
    });
    res.json({
        success: true,
        message: 'Account deleted successfully',
    });
}));
exports.default = router;
//# sourceMappingURL=users.js.map