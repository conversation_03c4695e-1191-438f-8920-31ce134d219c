{"version": 3, "file": "email.js", "sourceRoot": "", "sources": ["../../../src/utils/email.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAAoC;AAGpC,MAAa,YAAY;IAGvB;QACE,IAAI,CAAC,WAAW,GAAG,oBAAU,CAAC,iBAAiB,CAAC;YAC9C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;YAC3B,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC;YAC9C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM;YAC1C,IAAI,EAAE;gBACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;gBAC3B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;aAC5B;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,QAAuB;QACrC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC9B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;gBAC5B,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;aACpB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,8BAA8B,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,iCAAiC,CAAC,KAAa,EAAE,KAAa,EAAE,SAAiB;QAC/E,MAAM,eAAe,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,uBAAuB,KAAK,EAAE,CAAC;QAElF,OAAO;YACL,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,0BAA0B;YACnC,IAAI,EAAE;;;;;;;;;;;;;;;yDAe6C,SAAS;;;;;yBAKzC,eAAe;;;;gEAIwB,eAAe;;;;;;;;;;;;OAYxE;YACD,IAAI,EAAE;;;aAGC,SAAS;;;;UAIZ,eAAe;;;;;;;OAOlB;SACF,CAAC;IACJ,CAAC;IAED,6BAA6B,CAAC,KAAa,EAAE,KAAa,EAAE,SAAiB;QAC3E,MAAM,QAAQ,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,yBAAyB,KAAK,EAAE,CAAC;QAE7E,OAAO;YACL,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,0BAA0B;YACnC,IAAI,EAAE;;;;;;;;;;;;;;;yDAe6C,SAAS;;;;;yBAKzC,QAAQ;;;;gEAI+B,QAAQ;;;;;;;;;;;;OAYjE;YACD,IAAI,EAAE;;;aAGC,SAAS;;;;UAIZ,QAAQ;;;;;;;OAOX;SACF,CAAC;IACJ,CAAC;IAED,+BAA+B,CAAC,KAAa,EAAE,SAAiB,EAAE,WAAiB,EAAE,gBAAwB;QAC3G,MAAM,UAAU,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,WAAW,CAAC;QAE1D,OAAO;YACL,EAAE,EAAE,KAAK;YACT,OAAO,EAAE,kCAAkC;YAC3C,IAAI,EAAE;;;;;;;;;;;;;;;yDAe6C,SAAS;;;;;iDAKjB,WAAW,CAAC,cAAc,EAAE;kDAC3B,gBAAgB;;;;yBAIzC,UAAU;;;;;;;;;;;;;OAa5B;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,KAAa,EAAE,KAAa,EAAE,SAAiB;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QACjF,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,KAAa,EAAE,SAAiB;QACrE,MAAM,QAAQ,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAC7E,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,KAAa,EAAE,SAAiB,EAAE,WAAiB,EAAE,gBAAwB;QACrG,MAAM,QAAQ,GAAG,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;QACvG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;CACF;AAxND,oCAwNC;AAEY,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC"}