{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,2CAAwC;AACxC,6BAA+B;AAO/B,MAAa,WAAY,SAAQ,KAAK;IAIpC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,gBAAyB,IAAI;QAClF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAXD,kCAWC;AAEM,MAAM,YAAY,GAAG,CAC1B,KAAyE,EACzE,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,IAAI,OAAO,GAAG,uBAAuB,CAAC;IACtC,IAAI,OAAO,GAAQ,SAAS,CAAC;IAG7B,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE;QAC/B,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;KACnB,CAAC,CAAC;IAGH,IAAI,KAAK,YAAY,WAAW,EAAE,CAAC;QACjC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAC9B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC1B,CAAC;SAAM,IAAI,KAAK,YAAY,cAAQ,EAAE,CAAC;QACrC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,kBAAkB,CAAC;QAC7B,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACjC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACzB,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC,CAAC;IACN,CAAC;SAAM,IAAI,KAAK,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;QACjE,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,6BAA6B,CAAC;gBACxC,OAAO,GAAG;oBACR,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM;oBACzB,OAAO,EAAE,yCAAyC;iBACnD,CAAC;gBACF,MAAM;YACR,KAAK,OAAO;gBACV,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,kBAAkB,CAAC;gBAC7B,MAAM;YACR,KAAK,OAAO;gBACV,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,kCAAkC,CAAC;gBAC7C,MAAM;YACR,KAAK,OAAO;gBACV,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,kBAAkB,CAAC;gBAC7B,MAAM;YACR;gBACE,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,gBAAgB,CAAC;gBAC3B,MAAM;QACV,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,YAAY,eAAM,CAAC,2BAA2B,EAAE,CAAC;QAC/D,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,uBAAuB,CAAC;IACpC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;QACxC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,mBAAmB,CAAC;QAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC7C,OAAO,GAAG,qBAAqB,CAAC;QAClC,CAAC;IACH,CAAC;IAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAChE,OAAO,GAAG,sBAAsB,CAAC;QACjC,OAAO,GAAG,SAAS,CAAC;IACtB,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,OAAO;QACd,OAAO;QACP,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI;YAC5C,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC;KACH,CAAC,CAAC;AACL,CAAC,CAAC;AAzFW,QAAA,YAAY,gBAyFvB;AAEK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAEK,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC1E,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,SAAS,GAAG,CAAC,WAAW,YAAY,EAAE,GAAG,CAAC,CAAC;IACzE,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC;AAHW,QAAA,QAAQ,YAGnB"}