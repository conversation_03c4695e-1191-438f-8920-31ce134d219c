
'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChartHorizontalBig, CheckCircle, TrendingUp, CalendarDays, BookOpenText } from 'lucide-react';
import { PLACEHOLDER_PROGRESS_DATA } from '@/lib/constants';
import { Progress } from "@/components/ui/progress";
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, CartesianGrid } from 'recharts';


// Sample data for mood chart (replace with actual data structure later)
const moodChartData = [
  { name: 'Mon', mood: 7 },
  { name: 'Tue', mood: 6 },
  { name: 'Wed', mood: 8 },
  { name: 'Thu', mood: 5 },
  { name: 'Fri', mood: 7 },
  { name: 'Sat', mood: 9 },
  { name: 'Sun', mood: 6 },
];


export function ProgressTracker() {
  const { sessionsCompleted, exercisesDone, moodAverage, journalStreak } = PLACEHOLDER_PROGRESS_DATA;

  // Calculate progress percentages for demo (assuming targets)
  const sessionProgress = (sessionsCompleted / 10) * 100; // Assuming target of 10 sessions
  const exerciseProgress = (exercisesDone / 10) * 100; // Assuming target of 10 exercises

  return (
    <div className="space-y-8">
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl flex items-center gap-2"><BarChartHorizontalBig className="h-7 w-7 text-primary"/>Your Wellness Journey</CardTitle>
          <CardDescription>Track your engagement with therapy tools and monitor your progress over time.</CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2"><CheckCircle className="text-green-500"/>Activities Completed</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium">Guided Sessions</span>
                <span className="text-sm text-muted-foreground">{sessionsCompleted} / 10</span>
              </div>
              <Progress value={sessionProgress} aria-label={`${sessionProgress}% sessions completed`} />
            </div>
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium">CBT Exercises</span>
                <span className="text-sm text-muted-foreground">{exercisesDone} / 10</span>
              </div>
              <Progress value={exerciseProgress} aria-label={`${exerciseProgress}% exercises completed`} />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2"><CalendarDays className="text-blue-500"/>Journaling Streak</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-4xl font-bold text-primary">{journalStreak}</p>
            <p className="text-sm text-muted-foreground">consecutive days</p>
          </CardContent>
        </Card>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl flex items-center gap-2"><TrendingUp className="text-primary"/>Mood Trend (Last 7 Days)</CardTitle>
          <CardDescription>Visualize your mood fluctuations. (Placeholder data)</CardDescription>
        </CardHeader>
        <CardContent>
          {typeof window !== 'undefined' ? ( // Ensure Recharts only renders on client
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={moodChartData} margin={{ top: 5, right: 20, left: -20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                  <XAxis dataKey="name" stroke="hsl(var(--muted-foreground))" fontSize={12} />
                  <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} domain={[0, 10]} />
                  <Tooltip
                    contentStyle={{ 
                        backgroundColor: 'hsl(var(--popover))', 
                        borderColor: 'hsl(var(--popover-foreground))',
                        borderRadius: 'var(--radius)',
                     }}
                    labelStyle={{ color: 'hsl(var(--popover-foreground))' }}
                    itemStyle={{ color: 'hsl(var(--primary))' }}
                  />
                  <Bar dataKey="mood" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} barSize={30}/>
                </BarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <p className="text-center text-muted-foreground py-10">Loading chart...</p>
          )}
        </CardContent>
      </Card>
       <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2"><BookOpenText className="text-yellow-500"/>Overall Summary (Placeholder)</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
                More detailed progress insights and goal tracking will be available here soon. Keep engaging with the app to see your journey unfold!
            </p>
          </CardContent>
        </Card>
    </div>
  );
}
