
'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { useJournal } from '@/contexts/journal-context';
import { getJournalInsights, type JournalInsightsOutput } from '@/ai/flows/journal-insights';
import { InsightsDisplay } from '@/components/insights/insights-display';
import { Loader2, Brain, AlertTriangle } from 'lucide-react'; // Changed Lightbulb to Brain, imported AlertTriangle
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function InsightsPage() {
  const { journalEntries, getJournalEntriesText } = useJournal();
  const [insights, setInsights] = useState<JournalInsightsOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGetInsights = async () => {
    setIsLoading(true);
    setError(null);
    setInsights(null);

    if (journalEntries.length === 0) {
      setError("Please write some journal entries first to get insights.");
      setIsLoading(false);
      return;
    }

    try {
      const entriesText = getJournalEntriesText();
      const result = await getJournalInsights({ journalEntries: entriesText });
      setInsights(result);
    } catch (err) {
      console.error("Error getting insights:", err);
      setError("Failed to generate insights. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 max-w-3xl">
      <div className="flex items-center justify-center mb-10">
        <Brain className="h-10 w-10 text-primary mr-3" /> {/* Changed Lightbulb to Brain */}
        <h1 className="text-3xl font-bold text-center text-primary">AI Journal Insights</h1>
      </div>
      <p className="text-center text-muted-foreground mb-8">
        Discover patterns and insights from your journal entries with the help of AI.
      </p>

      <Card className="mb-8 shadow-md">
        <CardHeader>
          <CardTitle>Generate Insights</CardTitle>
          <CardDescription>
            Click the button below to analyze your journal entries. This may take a few moments.
            You need at least one journal entry.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleGetInsights} disabled={isLoading || journalEntries.length === 0} className="w-full">
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              'Get Insights from My Journal'
            )}
          </Button>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" /> {/* Icon should now render */}
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {insights && <InsightsDisplay insights={insights} />}
      
      {!isLoading && !insights && !error && journalEntries.length > 0 && (
        <p className="text-center text-muted-foreground mt-8">Click "Get Insights" to see your analysis.</p>
      )}
       {!isLoading && !insights && !error && journalEntries.length === 0 && (
        <p className="text-center text-muted-foreground mt-8">Add some journal entries to enable insights.</p>
      )}
    </div>
  );
}
