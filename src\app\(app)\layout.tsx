
'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  SidebarProvider,
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarTrigger,
  SidebarInset,
} from '@/components/ui/sidebar';
import { APP_NAME, NAVIGATION_LINKS } from '@/lib/constants';
import { Logo } from '@/components/icons/logo';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Menu, Sun, Moon, UserCircle2 } from 'lucide-react'; // Assuming Menu icon is used for mobile trigger

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  // Simple theme toggle example, replace with a robust solution if needed
  const [isDarkMode, setIsDarkMode] = useState(false);

  React.useEffect(() => {
    // Check initial theme preference on mount
    const storedTheme = localStorage.getItem('theme');
    if (storedTheme === 'dark') {
      document.documentElement.classList.add('dark');
      setIsDarkMode(true);
    } else {
      document.documentElement.classList.remove('dark');
      setIsDarkMode(false);
    }
  }, []);


  const toggleTheme = () => {
    setIsDarkMode(prevMode => {
      const newMode = !prevMode;
      if (newMode) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      }
      return newMode;
    });
  };

  return (
    <SidebarProvider defaultOpen>
      <div className="flex min-h-screen bg-background">
        <Sidebar collapsible="icon" className="border-r">
          <SidebarHeader className="p-4">
            <Link href="/" className="flex items-center gap-2"> {/* Changed href to "/" */}
              <Logo className="h-8 w-8" />
              <h1 className="text-xl font-semibold text-primary group-data-[collapsible=icon]:hidden">
                {APP_NAME}
              </h1>
            </Link>
          </SidebarHeader>
          <SidebarContent asChild>
            <ScrollArea className="h-full">
            <SidebarMenu>
              {NAVIGATION_LINKS.map((link) => (
                <SidebarMenuItem key={link.href}>
                  <SidebarMenuButton
                    asChild
                    isActive={pathname === link.href || (link.href !== "/journal" && link.href !== "/" && pathname.startsWith(link.href))}
                    tooltip={{ children: link.label, className:"bg-popover text-popover-foreground border shadow-md" }}
                  >
                    <Link href={link.href}>
                      <link.icon className="h-5 w-5" />
                      <span>{link.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
            </ScrollArea>
          </SidebarContent>
          <SidebarFooter className="p-4">
            <Button variant="ghost" onClick={toggleTheme} className="w-full justify-start gap-2 group-data-[collapsible=icon]:justify-center">
              {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              <span className="group-data-[collapsible=icon]:hidden">Toggle Theme</span>
            </Button>
          </SidebarFooter>
        </Sidebar>

        <SidebarInset className="flex flex-col">
          <header className="sticky top-0 z-10 flex h-14 items-center justify-between gap-4 border-b bg-background px-4 sm:justify-end sm:px-6">
            <div className="sm:hidden">
              <SidebarTrigger asChild>
                 <Button variant="outline" size="icon">
                    <Menu className="h-5 w-5" />
                 </Button>
              </SidebarTrigger>
            </div>
            <Link href="/profile">
              <Button variant="ghost" size="icon" aria-label="Profile">
                <UserCircle2 className="h-8 w-8 text-primary" /> {/* Increased size from h-6 w-6 */}
              </Button>
            </Link>
          </header>
          <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
            {children}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}

