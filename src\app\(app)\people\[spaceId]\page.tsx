
'use client';

import React, { useEffect, useState } from 'react';
import { useParams, notFound } from 'next/navigation';
import Image from 'next/image';
import { PLACEHOLDER_SPACES } from '@/lib/constants';
import type { Space } from '@/lib/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'; // Added CardTitle
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Users, Tag, Edit, Settings, Shield, BarChart3, CalendarClock, FolderKanban, MessageSquare, CreditCard, ShieldCheck, Award } from 'lucide-react';

import { LiveEventsSection } from '@/components/people/live-events-section';
import { ContentSharingSection } from '@/components/people/content-sharing-section';
import { DiscussionsSection } from '@/components/people/discussions-section';
import { SubscriptionTiersSection } from '@/components/people/subscription-tiers-section';
import { SpaceEngagementSafetySection } from '@/components/people/space-engagement-safety-section';


export default function SpaceDetailPage() {
  const params = useParams();
  const spaceId = params.spaceId as string;
  const [space, setSpace] = useState<Space | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching space data
    const foundSpace = PLACEHOLDER_SPACES.find(s => s.id === spaceId);
    if (foundSpace) {
      setSpace(foundSpace);
    }
    setIsLoading(false);
  }, [spaceId]);

  if (isLoading) {
    return <div className="container mx-auto py-8 text-center">Loading space details...</div>;
  }

  if (!space) {
    notFound();
    return null;
  }

  const hostInitials = space.host.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'H';

  // Assuming the current user is the host for demo purposes
  const isHost = true; 

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header Section */}
      <Card className="shadow-xl overflow-hidden">
        <div className="relative h-48 md:h-64 w-full">
          <Image
            src={space.bannerImageUrl}
            alt={`${space.name} banner`}
            layout="fill"
            objectFit="cover"
            priority
            data-ai-hint={space.aiBannerHint || "community banner"}
          />
           <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
        </div>
        <CardContent className="relative p-6 pt-0 -mt-16 z-10">
          <div className="flex flex-col sm:flex-row items-center sm:items-end gap-4">
            <Avatar className="h-32 w-32 border-4 border-background bg-background shadow-lg">
              <AvatarImage src={space.profileImageUrl} alt={space.name} data-ai-hint={space.aiProfileHint || "space logo"}/>
              <AvatarFallback className="text-4xl">{space.name.charAt(0).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div className="flex-grow text-center sm:text-left">
              <h1 className="text-3xl font-bold text-foreground">{space.name}</h1>
              <div className="flex items-center text-sm text-muted-foreground mt-1 justify-center sm:justify-start">
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarImage src={space.host.avatarUrl} alt={space.host.name} data-ai-hint={space.host.aiHostAvatarHint || "host avatar"} />
                  <AvatarFallback>{hostInitials}</AvatarFallback>
                </Avatar>
                Hosted by {space.host.name}
              </div>
            </div>
            {isHost && (
              <Button variant="outline" size="sm" className="mt-4 sm:mt-0">
                <Edit className="mr-2 h-4 w-4" /> Edit Space
              </Button>
            )}
          </div>
          <p className="text-muted-foreground mt-4 text-center sm:text-left max-w-2xl">{space.description}</p>
          <div className="mt-3 flex flex-wrap gap-2 justify-center sm:justify-start">
            {space.tags.map(tag => (
              <Badge key={tag} variant="secondary">
                <Tag className="mr-1 h-3 w-3" /> {tag}
              </Badge>
            ))}
          </div>
           <div className="mt-3 text-sm text-muted-foreground flex items-center justify-center sm:justify-start">
              <Users className="mr-1.5 h-4 w-4" />
              {space.followerCount.toLocaleString()} followers
              <span className="mx-2">·</span>
              {space.isSubscriptionBased ? (
                <Badge variant="outline" className="text-accent border-accent">
                  <CreditCard className="mr-1.5 h-3 w-3" /> Subscription Based
                </Badge>
                ) : (
                <Badge variant="outline" className="text-green-600 dark:text-green-400 border-green-500">
                    Free to Join
                </Badge>
              )}
            </div>
        </CardContent>
      </Card>

      {/* Tabs for Sections */}
      <Tabs defaultValue="discussions" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-5 mb-6">
          <TabsTrigger value="discussions"><MessageSquare className="mr-2 h-4 w-4"/>Discussions</TabsTrigger>
          <TabsTrigger value="content"><FolderKanban className="mr-2 h-4 w-4"/>Content</TabsTrigger>
          <TabsTrigger value="events"><CalendarClock className="mr-2 h-4 w-4"/>Events</TabsTrigger>
          <TabsTrigger value="tiers"><CreditCard className="mr-2 h-4 w-4"/>Membership</TabsTrigger>
          <TabsTrigger value="engagement"><BarChart3 className="mr-2 h-4 w-4"/>Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="discussions">
          <DiscussionsSection spaceId={space.id} />
        </TabsContent>
        <TabsContent value="content">
          <ContentSharingSection spaceId={space.id} />
        </TabsContent>
        <TabsContent value="events">
          <LiveEventsSection spaceId={space.id} />
        </TabsContent>
        <TabsContent value="tiers">
          <SubscriptionTiersSection space={space} />
        </TabsContent>
         <TabsContent value="engagement">
          <SpaceEngagementSafetySection />
        </TabsContent>
      </Tabs>
       {isHost && (
        <Card className="mt-8 shadow-md">
            <CardHeader>
                <CardTitle className="flex items-center text-xl"><Settings className="mr-2 h-5 w-5 text-primary"/>Host Admin Panel</CardTitle>
                <p className="text-sm text-muted-foreground">Manage your space settings, members, and content. (Placeholder)</p>
            </CardHeader>
            <CardContent className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm"><Users className="mr-2 h-4 w-4"/>Manage Members</Button>
                <Button variant="outline" size="sm"><Shield className="mr-2 h-4 w-4"/>Moderation Tools</Button>
                <Button variant="outline" size="sm"><BarChart3 className="mr-2 h-4 w-4"/>Analytics</Button>
            </CardContent>
        </Card>
      )}
    </div>
  );
}
