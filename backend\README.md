# WELL Platform Backend

A comprehensive mental health platform backend built with Node.js, Express, TypeScript, Prisma, and MySQL.

## 🚀 Features

### ✅ Implemented Core Features

- **Authentication System**
  - JWT-based authentication with refresh tokens
  - Role-based access control (Patient, Psychiatrist, Moderator, Admin)
  - Email verification and password reset
  - Two-factor authentication (2FA) support
  - Secure password hashing with bcrypt

- **User Management**
  - User registration with role selection
  - Profile management for all user types
  - Admin approval workflow for psychiatrists
  - User status management (active, suspended, banned)

- **Session Management**
  - Session booking and scheduling
  - Jitsi Meet integration for video calls
  - Session status tracking
  - Payment integration ready

- **Real-time Communication**
  - WebSocket support with Socket.IO
  - Private messaging between patients and psychiatrists
  - Session-based chat
  - Typing indicators and read receipts
  - File sharing support

- **Forum System**
  - Anonymous posting capability
  - Category-based organization
  - Comment threading
  - Content moderation tools

- **Admin Dashboard**
  - System statistics and analytics
  - User management
  - Psychiatrist approval workflow
  - System settings management

- **Security & Middleware**
  - Rate limiting
  - CORS configuration
  - Helmet security headers
  - Request logging
  - Comprehensive error handling

## 🛠 Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: MySQL with Prisma ORM
- **Authentication**: JWT + bcrypt
- **Real-time**: Socket.IO
- **Validation**: Zod
- **Email**: Nodemailer
- **File Upload**: Multer (ready)
- **Payment**: Stripe (ready)
- **Video**: Jitsi Meet API (ready)

## 📋 Prerequisites

- Node.js 18+ 
- MySQL 8.0+
- npm or yarn

## 🔧 Installation & Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

Copy the example environment file and configure it:

```bash
cp ../.env.example ../.env
```

Update the `.env` file with your configuration:

```env
# Database
DATABASE_URL="mysql://username:password@localhost:3306/well_db"

# JWT Secrets (CHANGE THESE IN PRODUCTION!)
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Other configurations...
```

### 3. Database Setup

Create the MySQL database:

```sql
CREATE DATABASE well_db;
```

Push the schema to the database:

```bash
npx prisma db push
```

Generate Prisma client:

```bash
npx prisma generate
```

Seed the database with initial data:

```bash
npm run db:seed
```

### 4. Start the Server

Development mode:
```bash
npm run dev:backend
```

The server will start on `http://localhost:3001`

## 📚 API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/verify-email` - Email verification
- `POST /api/auth/forgot-password` - Password reset request
- `POST /api/auth/reset-password` - Password reset
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout

### User Management

- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `PUT /api/users/password` - Change password
- `POST /api/users/two-factor` - Setup/disable 2FA
- `GET /api/users/stats` - User statistics
- `DELETE /api/users/account` - Delete account

### Sessions

- `POST /api/sessions` - Create session (patients)
- `GET /api/sessions` - Get user sessions
- `GET /api/sessions/:id` - Get specific session
- `PUT /api/sessions/:id` - Update session
- `DELETE /api/sessions/:id` - Cancel session

### Messages

- `POST /api/messages` - Send message
- `GET /api/messages` - Get messages
- `GET /api/messages/conversations` - Get conversations
- `PUT /api/messages/:id/read` - Mark as read

### Forum

- `GET /api/forum/posts` - Get forum posts
- `POST /api/forum/posts` - Create post
- `GET /api/forum/posts/:id` - Get specific post
- `GET /api/forum/categories` - Get categories

### Psychiatrists

- `GET /api/psychiatrists` - Get verified psychiatrists
- `GET /api/psychiatrists/:id` - Get psychiatrist details
- `PUT /api/psychiatrists/profile` - Update profile (psychiatrists)
- `PUT /api/psychiatrists/availability` - Update availability

### Admin (Admin only)

- `GET /api/admin/stats` - System statistics
- `GET /api/admin/pending-approvals` - Pending psychiatrists
- `PUT /api/admin/approve-psychiatrist/:id` - Approve psychiatrist
- `GET /api/admin/users` - Get all users
- `PUT /api/admin/users/:id/status` - Update user status
- `GET /api/admin/settings` - Get system settings
- `PUT /api/admin/settings/:key` - Update setting

## 🔐 Default Test Accounts

After seeding the database, you can use these accounts:

- **Admin**: <EMAIL> / Admin123!@#
- **Moderator**: <EMAIL> / Moderator123!@#
- **Psychiatrist**: <EMAIL> / Psychiatrist123!@#
- **Patient**: <EMAIL> / Patient123!@#

## 🧪 Testing the API

### Health Check
```bash
curl http://localhost:3001/health
```

### Register a new user
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "firstName": "Test",
    "lastName": "User",
    "role": "PATIENT"
  }'
```

### Login
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Patient123!@#"
  }'
```

## 🔄 Real-time Features

The backend supports real-time communication via Socket.IO:

- **Connection**: Connect with JWT token in auth header
- **Events**: 
  - `session:join` - Join a session room
  - `message:send` - Send a message
  - `typing:start/stop` - Typing indicators
  - `message:read` - Mark message as read

## 🚧 TODO / Future Enhancements

- [ ] Stripe payment integration
- [ ] AI content moderation
- [ ] Crisis detection system
- [ ] File upload handling
- [ ] Email notification system
- [ ] Advanced analytics
- [ ] API rate limiting per user
- [ ] Comprehensive logging
- [ ] Unit and integration tests
- [ ] Docker containerization
- [ ] CI/CD pipeline

## 🐛 Troubleshooting

### Database Connection Issues
- Ensure MySQL is running
- Check DATABASE_URL in .env
- Verify database exists and user has permissions

### TypeScript Errors
- Run `npx prisma generate` after schema changes
- Clear node_modules and reinstall if needed

### Port Already in Use
- Change PORT in .env file
- Kill existing processes on port 3001

## 📝 License

This project is part of the WELL mental health platform.
