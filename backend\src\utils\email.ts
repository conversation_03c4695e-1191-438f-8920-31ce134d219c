import nodemailer from 'nodemailer';
import { EmailTemplate } from '../types';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  async sendEmail(template: EmailTemplate): Promise<void> {
    try {
      await this.transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: template.to,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });
      
      console.log(`Email sent successfully to ${template.to}`);
    } catch (error) {
      console.error('Failed to send email:', error);
      throw new Error('Failed to send email');
    }
  }

  generateEmailVerificationTemplate(email: string, token: string, firstName: string): EmailTemplate {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
    
    return {
      to: email,
      subject: 'Verify Your WELL Account',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Email</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to WELL</h1>
            <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">Your Mental Wellness Companion</p>
          </div>
          
          <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #333; margin-top: 0;">Hi ${firstName}!</h2>
            
            <p>Thank you for joining WELL. To complete your registration and start your mental wellness journey, please verify your email address.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Verify Email Address</a>
            </div>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #667eea;">${verificationUrl}</p>
            
            <p style="margin-top: 30px; font-size: 14px; color: #666;">
              This verification link will expire in 24 hours. If you didn't create an account with WELL, you can safely ignore this email.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #999;">
            <p>© 2024 WELL Platform. All rights reserved.</p>
          </div>
        </body>
        </html>
      `,
      text: `
        Welcome to WELL!
        
        Hi ${firstName},
        
        Thank you for joining WELL. To complete your registration, please verify your email address by clicking the link below:
        
        ${verificationUrl}
        
        This verification link will expire in 24 hours.
        
        If you didn't create an account with WELL, you can safely ignore this email.
        
        © 2024 WELL Platform. All rights reserved.
      `,
    };
  }

  generatePasswordResetTemplate(email: string, token: string, firstName: string): EmailTemplate {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    
    return {
      to: email,
      subject: 'Reset Your WELL Password',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Password Reset</h1>
            <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">WELL Platform</p>
          </div>
          
          <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #333; margin-top: 0;">Hi ${firstName}!</h2>
            
            <p>We received a request to reset your password for your WELL account. If you made this request, click the button below to reset your password.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Reset Password</a>
            </div>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #667eea;">${resetUrl}</p>
            
            <p style="margin-top: 30px; font-size: 14px; color: #666;">
              This password reset link will expire in 1 hour. If you didn't request a password reset, you can safely ignore this email.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #999;">
            <p>© 2024 WELL Platform. All rights reserved.</p>
          </div>
        </body>
        </html>
      `,
      text: `
        Password Reset - WELL Platform
        
        Hi ${firstName},
        
        We received a request to reset your password. If you made this request, click the link below to reset your password:
        
        ${resetUrl}
        
        This password reset link will expire in 1 hour.
        
        If you didn't request a password reset, you can safely ignore this email.
        
        © 2024 WELL Platform. All rights reserved.
      `,
    };
  }

  generateSessionReminderTemplate(email: string, firstName: string, sessionDate: Date, psychiatristName: string): EmailTemplate {
    const sessionUrl = `${process.env.FRONTEND_URL}/sessions`;
    
    return {
      to: email,
      subject: 'Session Reminder - WELL Platform',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Session Reminder</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Session Reminder</h1>
            <p style="color: white; margin: 10px 0 0 0; font-size: 16px;">WELL Platform</p>
          </div>
          
          <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #333; margin-top: 0;">Hi ${firstName}!</h2>
            
            <p>This is a reminder that you have an upcoming session scheduled:</p>
            
            <div style="background: white; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #667eea;">
              <p><strong>Date & Time:</strong> ${sessionDate.toLocaleString()}</p>
              <p><strong>Psychiatrist:</strong> ${psychiatristName}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${sessionUrl}" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Join Session</a>
            </div>
            
            <p style="margin-top: 30px; font-size: 14px; color: #666;">
              Please join the session on time. If you need to reschedule, please contact your psychiatrist as soon as possible.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #999;">
            <p>© 2024 WELL Platform. All rights reserved.</p>
          </div>
        </body>
        </html>
      `,
    };
  }

  async sendEmailVerification(email: string, token: string, firstName: string): Promise<void> {
    const template = this.generateEmailVerificationTemplate(email, token, firstName);
    await this.sendEmail(template);
  }

  async sendPasswordReset(email: string, token: string, firstName: string): Promise<void> {
    const template = this.generatePasswordResetTemplate(email, token, firstName);
    await this.sendEmail(template);
  }

  async sendSessionReminder(email: string, firstName: string, sessionDate: Date, psychiatristName: string): Promise<void> {
    const template = this.generateSessionReminderTemplate(email, firstName, sessionDate, psychiatristName);
    await this.sendEmail(template);
  }
}

export const emailService = new EmailService();
