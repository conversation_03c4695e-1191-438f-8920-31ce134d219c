"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = exports.prisma = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../.env') });
const client_1 = require("@prisma/client");
const errorHandler_1 = require("./src/middleware/errorHandler");
const requestLogger_1 = require("./src/middleware/requestLogger");
const auth_1 = require("./src/middleware/auth");
const auth_2 = __importDefault(require("./src/routes/auth"));
const users_1 = __importDefault(require("./src/routes/users"));
const sessions_1 = __importDefault(require("./src/routes/sessions"));
const messages_1 = __importDefault(require("./src/routes/messages"));
const payments_1 = __importDefault(require("./src/routes/payments"));
const forum_1 = __importDefault(require("./src/routes/forum"));
const admin_1 = __importDefault(require("./src/routes/admin"));
const psychiatrists_1 = __importDefault(require("./src/routes/psychiatrists"));
const socketHandlers_1 = require("./src/sockets/socketHandlers");
exports.prisma = new client_1.PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:9002",
        methods: ["GET", "POST"],
        credentials: true
    }
});
exports.io = io;
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || "http://localhost:9002",
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    message: {
        error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use(requestLogger_1.requestLogger);
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
    });
});
app.use('/api/auth', auth_2.default);
app.use('/api/users', auth_1.authMiddleware, users_1.default);
app.use('/api/sessions', auth_1.authMiddleware, sessions_1.default);
app.use('/api/messages', auth_1.authMiddleware, messages_1.default);
app.use('/api/payments', auth_1.authMiddleware, payments_1.default);
app.use('/api/forum', auth_1.authMiddleware, forum_1.default);
app.use('/api/admin', auth_1.authMiddleware, admin_1.default);
app.use('/api/psychiatrists', auth_1.authMiddleware, psychiatrists_1.default);
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl,
    });
});
app.use(errorHandler_1.errorHandler);
(0, socketHandlers_1.initializeSocketHandlers)(io);
process.on('SIGTERM', async () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('HTTP server closed');
        exports.prisma.$disconnect().then(() => {
            console.log('Database connection closed');
            process.exit(0);
        });
    });
});
process.on('SIGINT', async () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('HTTP server closed');
        exports.prisma.$disconnect().then(() => {
            console.log('Database connection closed');
            process.exit(0);
        });
    });
});
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
    console.log(`🔗 CORS origin: ${process.env.CORS_ORIGIN}`);
});
//# sourceMappingURL=server.js.map