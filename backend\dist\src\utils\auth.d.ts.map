{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../../src/utils/auth.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAEvE,qBAAa,SAAS;WACP,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;WAK/C,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAIxF,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,UAAU,GAAG,MAAM;IAgBvD,MAAM,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,GAAG,MAAM;IAgBxD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAQpD,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,GAAG;QAAE,WAAW,EAAE,MAAM,CAAC;QAAC,YAAY,EAAE,MAAM,CAAA;KAAE;IAahG,MAAM,CAAC,8BAA8B,IAAI,MAAM;IAI/C,MAAM,CAAC,0BAA0B,IAAI,MAAM;IAI3C,MAAM,CAAC,uBAAuB,IAAI;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,MAAM,CAAA;KAAE;IAYvE,MAAM,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO;IASnE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE;IA6BjF,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;IAK5C,MAAM,CAAC,iBAAiB,IAAI,MAAM;IAIlC,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAMrD,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,GAAG,iBAAiB;IAarD,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,iBAAiB,GAAG,YAAY;IAUhE,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,IAAI,QAAQ;IAIlD,MAAM,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,OAAO;IAIhF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;IASnD,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,GAAG,OAAO;IAKvE,MAAM,CAAC,cAAc,IAAI,MAAM;IAI/B,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM;CAG1C"}