
'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Award, <PERSON><PERSON>he<PERSON>, <PERSON><PERSON><PERSON>3, Eye } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

export function SpaceEngagementSafetySection() {
  // Placeholder data
  const userEngagement = {
    postsMade: 5,
    comments: 12,
    eventsAttended: 2,
    badgesEarned: ['Active Member', 'Early Supporter'],
  };
  const progressToNextBadge = 60; // Example percentage

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Engagement & Recognition Card */}
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center text-xl">
            <Award className="mr-2 h-6 w-6 text-primary" />
            Your Engagement
          </CardTitle>
          <CardDescription>
            Track your activity and badges earned within this space.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold text-sm mb-1">Activity Stats:</h4>
            <ul className="list-disc list-inside text-xs text-muted-foreground space-y-0.5">
              <li>Posts Created: {userEngagement.postsMade}</li>
              <li>Comments Made: {userEngagement.comments}</li>
              <li>Events Attended: {userEngagement.eventsAttended}</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-sm mb-1">Badges Earned:</h4>
            {userEngagement.badgesEarned.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {userEngagement.badgesEarned.map(badge => (
                  <span key={badge} className="px-2 py-0.5 text-xs bg-accent text-accent-foreground rounded-full">
                    {badge}
                  </span>
                ))}
              </div>
            ) : (
              <p className="text-xs text-muted-foreground">No badges earned yet. Keep engaging!</p>
            )}
          </div>
          <div>
             <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium">Progress to "Super Fan" Badge</span>
                <span className="text-sm text-muted-foreground">{progressToNextBadge}%</span>
              </div>
            <Progress value={progressToNextBadge} aria-label={`${progressToNextBadge}% towards next badge`} />
          </div>
           <p className="text-xs text-muted-foreground pt-2">
            Recognition systems like badges can be implemented to reward active and supportive members.
          </p>
        </CardContent>
      </Card>

      {/* Safety & Moderation Card */}
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center text-xl">
            <ShieldCheck className="mr-2 h-6 w-6 text-green-600" />
            Safety & Moderation
          </CardTitle>
          <CardDescription>
            Our commitment to a safe and supportive environment.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <p className="text-sm text-muted-foreground">
            This space is actively moderated to ensure a positive experience for all members.
            Hosts can assign trusted moderators.
          </p>
          <ul className="list-disc list-inside text-xs text-muted-foreground space-y-0.5">
            <li>Report inappropriate content or behavior.</li>
            <li>Hosts can mute or ban users violating community guidelines.</li>
            <li>Clear community guidelines are established by the host.</li>
          </ul>
          <p className="text-xs text-muted-foreground pt-2">
            Robust moderation tools are essential for maintaining healthy online communities.
            These include reporting mechanisms, user blocking, and content filtering.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
