
import React from 'react';
import Image from 'next/image';
import type { ForumPost } from '@/lib/types';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { MessageSquare, ThumbsUp, CalendarDays } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ForumPostCardProps {
  post: ForumPost;
}

export function ForumPostCard({ post }: ForumPostCardProps) {
  const { toast } = useToast();
  const authorInitial = post.author ? post.author.charAt(0).toUpperCase() : '?';

  const handleLike = () => {
    toast({
      title: "Post Liked",
      description: "You've liked this post.",
      variant: "default",
    });
  };

  const handleReply = () => {
    toast({
      title: "Reply (Simulated)",
      description: "Actual reply functionality is not yet implemented.",
      variant: "default",
    });
  };

  return (
    <Card className="shadow-md hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex items-start space-x-3 mb-2">
          <Avatar>
            {post.avatarUrl ? (
              <AvatarImage src={post.avatarUrl} alt={post.author || 'User avatar'} data-ai-hint={post.aiHint || "profile picture"} />
            ) : (
              <AvatarFallback>{authorInitial}</AvatarFallback>
            )}
          </Avatar>
          <div className="flex-1">
            {/* Author name display removed as per request */}
            <CardTitle className="text-lg font-semibold mb-1">{post.title}</CardTitle>
            <p className="text-xs text-muted-foreground flex items-center">
              <CalendarDays className="mr-1 h-3 w-3" />
              {new Date(post.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-foreground/90 whitespace-pre-wrap">{post.content}</p>
      </CardContent>
      <CardFooter className="flex justify-between items-center">
        <div className="flex space-x-4">
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-primary" onClick={handleLike}>
            <ThumbsUp className="mr-2 h-4 w-4" /> Like
          </Button>
          <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-primary" onClick={handleReply}>
            <MessageSquare className="mr-2 h-4 w-4" /> Reply
          </Button>
        </div>
        {/* Placeholder for reply count or other interaction */}
        {/* <span className="text-xs text-muted-foreground">12 replies</span> */}
      </CardFooter>
    </Card>
  );
}
