
import React from 'react';
import type { JournalEntry } from '@/lib/types';
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { CalendarDays, Smile, Meh, Frown, Angry, SparklesIcon, HelpCircle, Trash2 } from 'lucide-react';
import { useJournal } from '@/contexts/journal-context';
import { useToast } from '@/hooks/use-toast';

const MoodIcon = ({ mood }: { mood: JournalEntry['mood'] }) => {
  switch (mood) {
    case 'happy': return <Smile className="h-5 w-5 text-yellow-500" />;
    case 'excited': return <SparklesIcon className="h-5 w-5 text-orange-500" />;
    case 'calm': return <Smile className="h-5 w-5 text-green-500" />;
    case 'sad': return <Frown className="h-5 w-5 text-blue-500" />;
    case 'anxious': return <Meh className="h-5 w-5 text-purple-500" />;
    case 'angry': return <Angry className="h-5 w-5 text-red-500" />;
    case 'neutral': return <Meh className="h-5 w-5 text-gray-500" />;
    default: return <HelpCircle className="h-5 w-5 text-gray-400" />;
  }
};

interface JournalEntryCardProps {
  entry: JournalEntry;
}

export function JournalEntryCard({ entry }: JournalEntryCardProps) {
  const { deleteJournalEntry } = useJournal();
  const { toast } = useToast();

  const handleDelete = () => {
    deleteJournalEntry(entry.id);
    toast({
      title: "Entry Deleted",
      description: "Your journal entry has been removed.",
      variant: "destructive",
    });
  };

  return (
    <Card className="shadow-md hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-semibold">{entry.title}</CardTitle>
            <CardDescription className="flex items-center text-sm text-muted-foreground pt-1">
              <CalendarDays className="mr-2 h-4 w-4" />
              {new Date(entry.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDelete}
            aria-label="Delete entry"
            className="text-destructive hover:bg-destructive/10 hover:text-destructive"
          >
            <Trash2 className="h-5 w-5" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-foreground/90 whitespace-pre-wrap">{entry.content}</p>
      </CardContent>
      <CardFooter>
        <Badge variant="outline" className="flex items-center gap-2 capitalize">
          <MoodIcon mood={entry.mood} />
          {entry.mood}
        </Badge>
      </CardFooter>
    </Card>
  );
}
