"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
const server_1 = require("../../server");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const sendMessageSchema = zod_1.z.object({
    receiverId: zod_1.z.string().cuid().optional(),
    sessionId: zod_1.z.string().cuid().optional(),
    content: zod_1.z.string().min(1, 'Message content is required').max(5000, 'Message too long'),
    messageType: zod_1.z.enum(['TEXT', 'IMAGE', 'FILE', 'SYSTEM']).optional().default('TEXT'),
    fileUrl: zod_1.z.string().url().optional(),
    fileName: zod_1.z.string().optional(),
});
const messageQuerySchema = zod_1.z.object({
    page: zod_1.z.string().transform(Number).optional().default(1),
    limit: zod_1.z.string().transform(Number).optional().default(50),
    receiverId: zod_1.z.string().cuid().optional(),
    sessionId: zod_1.z.string().cuid().optional(),
    before: zod_1.z.string().datetime().optional(),
    after: zod_1.z.string().datetime().optional(),
});
router.post('/', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = sendMessageSchema.parse(req.body);
    const senderId = req.user.id;
    if (!validatedData.receiverId && !validatedData.sessionId) {
        throw new errorHandler_1.CustomError('Either receiverId or sessionId must be provided', 400);
    }
    if (validatedData.receiverId) {
        const receiver = await server_1.prisma.user.findUnique({
            where: { id: validatedData.receiverId },
            select: { id: true, role: true, status: true },
        });
        if (!receiver) {
            throw new errorHandler_1.CustomError('Receiver not found', 404);
        }
        if (receiver.status !== 'ACTIVE') {
            throw new errorHandler_1.CustomError('Cannot send message to inactive user', 400);
        }
        const hasRelationship = await server_1.prisma.session.findFirst({
            where: {
                OR: [
                    { patientId: senderId, psychiatristId: validatedData.receiverId },
                    { patientId: validatedData.receiverId, psychiatristId: senderId },
                ],
            },
        });
        if (!hasRelationship && req.user.role !== client_1.UserRole.ADMIN && req.user.role !== client_1.UserRole.MODERATOR) {
            throw new errorHandler_1.CustomError('Cannot send message to this user', 403);
        }
    }
    if (validatedData.sessionId) {
        const session = await server_1.prisma.session.findUnique({
            where: { id: validatedData.sessionId },
            select: { id: true, patientId: true, psychiatristId: true, status: true },
        });
        if (!session) {
            throw new errorHandler_1.CustomError('Session not found', 404);
        }
        if (session.patientId !== senderId && session.psychiatristId !== senderId) {
            throw new errorHandler_1.CustomError('You are not a participant in this session', 403);
        }
    }
    const message = await server_1.prisma.message.create({
        data: {
            senderId,
            receiverId: validatedData.receiverId,
            sessionId: validatedData.sessionId,
            content: validatedData.content,
            messageType: validatedData.messageType,
            fileUrl: validatedData.fileUrl,
            fileName: validatedData.fileName,
        },
        include: {
            sender: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    profileImage: true,
                },
            },
            receiver: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                },
            },
        },
    });
    res.status(201).json({
        success: true,
        message: 'Message sent successfully',
        data: message,
    });
}));
router.get('/', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const query = messageQuerySchema.parse(req.query);
    const userId = req.user.id;
    const where = {};
    if (query.receiverId) {
        where.OR = [
            { senderId: userId, receiverId: query.receiverId },
            { senderId: query.receiverId, receiverId: userId },
        ];
    }
    else if (query.sessionId) {
        const session = await server_1.prisma.session.findUnique({
            where: { id: query.sessionId },
            select: { patientId: true, psychiatristId: true },
        });
        if (!session) {
            throw new errorHandler_1.CustomError('Session not found', 404);
        }
        if (session.patientId !== userId && session.psychiatristId !== userId) {
            throw new errorHandler_1.CustomError('Access denied', 403);
        }
        where.sessionId = query.sessionId;
    }
    else {
        where.OR = [
            { senderId: userId },
            { receiverId: userId },
        ];
    }
    if (query.before || query.after) {
        where.createdAt = {};
        if (query.before) {
            where.createdAt.lt = new Date(query.before);
        }
        if (query.after) {
            where.createdAt.gt = new Date(query.after);
        }
    }
    const [messages, total] = await Promise.all([
        server_1.prisma.message.findMany({
            where,
            include: {
                sender: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        profileImage: true,
                    },
                },
                receiver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
            orderBy: { createdAt: 'desc' },
            skip: (query.page - 1) * query.limit,
            take: query.limit,
        }),
        server_1.prisma.message.count({ where }),
    ]);
    res.json({
        success: true,
        data: {
            messages,
            pagination: {
                page: query.page,
                limit: query.limit,
                total,
                totalPages: Math.ceil(total / query.limit),
                hasNext: query.page * query.limit < total,
                hasPrev: query.page > 1,
            },
        },
    });
}));
router.get('/conversations', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const conversations = await server_1.prisma.$queryRaw `
    SELECT 
      CASE 
        WHEN m.senderId = ${userId} THEN m.receiverId 
        ELSE m.senderId 
      END as otherUserId,
      u.firstName,
      u.lastName,
      u.profileImage,
      u.role,
      m.content as lastMessage,
      m.messageType as lastMessageType,
      m.createdAt as lastMessageAt,
      m.isRead,
      COUNT(CASE WHEN m.receiverId = ${userId} AND m.isRead = false THEN 1 END) as unreadCount
    FROM messages m
    INNER JOIN users u ON (
      CASE 
        WHEN m.senderId = ${userId} THEN u.id = m.receiverId 
        ELSE u.id = m.senderId 
      END
    )
    WHERE (m.senderId = ${userId} OR m.receiverId = ${userId})
      AND m.sessionId IS NULL
    GROUP BY otherUserId, u.firstName, u.lastName, u.profileImage, u.role, m.content, m.messageType, m.createdAt, m.isRead
    HAVING m.createdAt = (
      SELECT MAX(m2.createdAt) 
      FROM messages m2 
      WHERE (
        (m2.senderId = ${userId} AND m2.receiverId = otherUserId) OR 
        (m2.senderId = otherUserId AND m2.receiverId = ${userId})
      ) AND m2.sessionId IS NULL
    )
    ORDER BY m.createdAt DESC
  `;
    res.json({
        success: true,
        data: conversations,
    });
}));
router.put('/:messageId/read', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { messageId } = req.params;
    const userId = req.user.id;
    const message = await server_1.prisma.message.findUnique({
        where: { id: messageId },
        select: { id: true, receiverId: true, isRead: true },
    });
    if (!message) {
        throw new errorHandler_1.CustomError('Message not found', 404);
    }
    if (message.receiverId !== userId) {
        throw new errorHandler_1.CustomError('Can only mark your own received messages as read', 403);
    }
    if (message.isRead) {
        return res.json({
            success: true,
            message: 'Message already marked as read',
        });
    }
    await server_1.prisma.message.update({
        where: { id: messageId },
        data: { isRead: true, readAt: new Date() },
    });
    res.json({
        success: true,
        message: 'Message marked as read',
    });
}));
router.put('/conversations/:otherUserId/read', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { otherUserId } = req.params;
    const userId = req.user.id;
    const otherUser = await server_1.prisma.user.findUnique({
        where: { id: otherUserId },
        select: { id: true },
    });
    if (!otherUser) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    await server_1.prisma.message.updateMany({
        where: {
            senderId: otherUserId,
            receiverId: userId,
            isRead: false,
            sessionId: null,
        },
        data: { isRead: true, readAt: new Date() },
    });
    res.json({
        success: true,
        message: 'All messages marked as read',
    });
}));
router.delete('/:messageId', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { messageId } = req.params;
    const userId = req.user.id;
    const message = await server_1.prisma.message.findUnique({
        where: { id: messageId },
        select: { id: true, senderId: true, content: true },
    });
    if (!message) {
        throw new errorHandler_1.CustomError('Message not found', 404);
    }
    if (message.senderId !== userId) {
        throw new errorHandler_1.CustomError('Can only delete your own messages', 403);
    }
    await server_1.prisma.message.update({
        where: { id: messageId },
        data: {
            content: '[Message deleted]',
            isEdited: true,
            editedAt: new Date(),
        },
    });
    res.json({
        success: true,
        message: 'Message deleted successfully',
    });
}));
exports.default = router;
//# sourceMappingURL=messages.js.map