"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
const server_1 = require("../../server");
const auth_1 = require("../utils/auth");
const email_1 = require("../utils/email");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_2 = require("../middleware/auth");
const router = (0, express_1.Router)();
const registerSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email address'),
    password: zod_1.z.string().min(8, 'Password must be at least 8 characters'),
    firstName: zod_1.z.string().min(1, 'First name is required'),
    lastName: zod_1.z.string().min(1, 'Last name is required'),
    role: zod_1.z.enum([client_1.UserRole.PATIENT, client_1.UserRole.PSYCHIATRIST]),
    phoneNumber: zod_1.z.string().optional(),
    dateOfBirth: zod_1.z.string().optional(),
});
const loginSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email address'),
    password: zod_1.z.string().min(1, 'Password is required'),
    twoFactorCode: zod_1.z.string().optional(),
});
const forgotPasswordSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email address'),
});
const resetPasswordSchema = zod_1.z.object({
    token: zod_1.z.string().min(1, 'Reset token is required'),
    password: zod_1.z.string().min(8, 'Password must be at least 8 characters'),
});
const verifyEmailSchema = zod_1.z.object({
    token: zod_1.z.string().min(1, 'Verification token is required'),
});
const refreshTokenSchema = zod_1.z.object({
    refreshToken: zod_1.z.string().min(1, 'Refresh token is required'),
});
router.post('/register', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = registerSchema.parse(req.body);
    const passwordValidation = auth_1.AuthUtils.validatePassword(validatedData.password);
    if (!passwordValidation.isValid) {
        throw new errorHandler_1.CustomError(passwordValidation.errors.join(', '), 400);
    }
    const existingUser = await server_1.prisma.user.findUnique({
        where: { email: validatedData.email.toLowerCase() },
    });
    if (existingUser) {
        throw new errorHandler_1.CustomError('User with this email already exists', 409);
    }
    const hashedPassword = await auth_1.AuthUtils.hashPassword(validatedData.password);
    const emailVerificationToken = auth_1.AuthUtils.generateEmailVerificationToken();
    const user = await server_1.prisma.user.create({
        data: {
            email: validatedData.email.toLowerCase(),
            password: hashedPassword,
            firstName: validatedData.firstName,
            lastName: validatedData.lastName,
            role: validatedData.role,
            phoneNumber: validatedData.phoneNumber,
            dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : null,
            emailVerificationToken,
            status: validatedData.role === client_1.UserRole.PSYCHIATRIST ? 'PENDING_APPROVAL' : 'PENDING',
        },
    });
    if (validatedData.role === client_1.UserRole.PATIENT) {
        await server_1.prisma.patientProfile.create({
            data: { userId: user.id },
        });
    }
    else if (validatedData.role === client_1.UserRole.PSYCHIATRIST) {
        await server_1.prisma.psychiatristProfile.create({
            data: {
                userId: user.id,
                licenseNumber: '',
                specialties: '[]',
                hourlyRate: 0,
            },
        });
    }
    try {
        await email_1.emailService.sendEmailVerification(user.email, emailVerificationToken, user.firstName);
    }
    catch (error) {
        console.error('Failed to send verification email:', error);
    }
    res.status(201).json({
        success: true,
        message: 'Registration successful. Please check your email for verification.',
        data: {
            userId: user.id,
            email: user.email,
            role: user.role,
            status: user.status,
        },
    });
}));
router.post('/login', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password, twoFactorCode } = loginSchema.parse(req.body);
    const user = await server_1.prisma.user.findUnique({
        where: { email: email.toLowerCase() },
    });
    if (!user) {
        throw new errorHandler_1.CustomError('Invalid credentials', 401);
    }
    const isPasswordValid = await auth_1.AuthUtils.comparePassword(password, user.password);
    if (!isPasswordValid) {
        throw new errorHandler_1.CustomError('Invalid credentials', 401);
    }
    if (user.status === 'SUSPENDED') {
        throw new errorHandler_1.CustomError('Account suspended', 403);
    }
    if (user.status === 'BANNED') {
        throw new errorHandler_1.CustomError('Account banned', 403);
    }
    if (user.twoFactorEnabled) {
        if (!twoFactorCode) {
            throw new errorHandler_1.CustomError('Two-factor authentication code required', 400);
        }
        if (!user.twoFactorSecret) {
            throw new errorHandler_1.CustomError('Two-factor authentication not properly configured', 500);
        }
        const isValidTwoFactor = auth_1.AuthUtils.verifyTwoFactorToken(twoFactorCode, user.twoFactorSecret);
        if (!isValidTwoFactor) {
            throw new errorHandler_1.CustomError('Invalid two-factor authentication code', 401);
        }
    }
    await server_1.prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
    });
    const authUser = auth_1.AuthUtils.sanitizeUserData(user);
    const authResponse = auth_1.AuthUtils.createAuthResponse(authUser);
    res.json({
        success: true,
        message: 'Login successful',
        data: authResponse,
    });
}));
router.post('/verify-email', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { token } = verifyEmailSchema.parse(req.body);
    const user = await server_1.prisma.user.findFirst({
        where: { emailVerificationToken: token },
    });
    if (!user) {
        throw new errorHandler_1.CustomError('Invalid or expired verification token', 400);
    }
    await server_1.prisma.user.update({
        where: { id: user.id },
        data: {
            emailVerified: true,
            emailVerificationToken: null,
            status: user.role === client_1.UserRole.PSYCHIATRIST ? 'PENDING_APPROVAL' : 'ACTIVE',
        },
    });
    res.json({
        success: true,
        message: 'Email verified successfully',
    });
}));
router.post('/forgot-password', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email } = forgotPasswordSchema.parse(req.body);
    const user = await server_1.prisma.user.findUnique({
        where: { email: email.toLowerCase() },
    });
    if (!user) {
        res.json({
            success: true,
            message: 'If an account with that email exists, a password reset link has been sent.',
        });
        return;
    }
    const resetToken = auth_1.AuthUtils.generatePasswordResetToken();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000);
    await server_1.prisma.user.update({
        where: { id: user.id },
        data: {
            passwordResetToken: resetToken,
            passwordResetExpires: resetExpires,
        },
    });
    try {
        await email_1.emailService.sendPasswordReset(user.email, resetToken, user.firstName);
    }
    catch (error) {
        console.error('Failed to send password reset email:', error);
        throw new errorHandler_1.CustomError('Failed to send password reset email', 500);
    }
    res.json({
        success: true,
        message: 'Password reset link sent to your email',
    });
}));
router.post('/reset-password', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { token, password } = resetPasswordSchema.parse(req.body);
    const passwordValidation = auth_1.AuthUtils.validatePassword(password);
    if (!passwordValidation.isValid) {
        throw new errorHandler_1.CustomError(passwordValidation.errors.join(', '), 400);
    }
    const user = await server_1.prisma.user.findFirst({
        where: {
            passwordResetToken: token,
            passwordResetExpires: {
                gt: new Date(),
            },
        },
    });
    if (!user) {
        throw new errorHandler_1.CustomError('Invalid or expired reset token', 400);
    }
    const hashedPassword = await auth_1.AuthUtils.hashPassword(password);
    await server_1.prisma.user.update({
        where: { id: user.id },
        data: {
            password: hashedPassword,
            passwordResetToken: null,
            passwordResetExpires: null,
        },
    });
    res.json({
        success: true,
        message: 'Password reset successful',
    });
}));
router.post('/refresh', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = refreshTokenSchema.parse(req.body);
    try {
        const decoded = auth_1.AuthUtils.verifyRefreshToken(refreshToken);
        const user = await server_1.prisma.user.findUnique({
            where: { id: decoded.userId },
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                status: true,
                emailVerified: true,
                twoFactorEnabled: true,
            },
        });
        if (!user || user.status === 'SUSPENDED' || user.status === 'BANNED') {
            throw new errorHandler_1.CustomError('Invalid refresh token', 401);
        }
        const authUser = auth_1.AuthUtils.sanitizeUserData(user);
        const tokens = auth_1.AuthUtils.generateTokenPair(authUser);
        res.json({
            success: true,
            data: {
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken,
            },
        });
    }
    catch (error) {
        throw new errorHandler_1.CustomError('Invalid refresh token', 401);
    }
}));
router.get('/me', auth_2.authMiddleware, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await server_1.prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            status: true,
            emailVerified: true,
            twoFactorEnabled: true,
            profileImage: true,
            phoneNumber: true,
            dateOfBirth: true,
            timezone: true,
            createdAt: true,
            lastLoginAt: true,
        },
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    res.json({
        success: true,
        data: user,
    });
}));
router.post('/logout', auth_2.authMiddleware, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({
        success: true,
        message: 'Logged out successfully',
    });
}));
exports.default = router;
//# sourceMappingURL=auth.js.map