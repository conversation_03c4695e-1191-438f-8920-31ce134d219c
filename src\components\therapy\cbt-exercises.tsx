
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, <PERSON><PERSON>Trigger, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { PLACEHOLDER_CBT_EXERCISES } from '@/lib/constants';
import type { CBTExercise } from '@/lib/types';
import * as LucideIcons from 'lucide-react'; // Import all icons
import { useToast } from '@/hooks/use-toast';

// Helper to get Lucide icon component by name
const getIcon = (iconName?: string): React.ElementType => {
  if (iconName && LucideIcons[iconName as keyof typeof LucideIcons]) {
    return LucideIcons[iconName as keyof typeof LucideIcons] as React.ElementType;
  }
  return LucideIcons.Puzzle; // Default icon
};


function ExerciseCard({ exercise }: { exercise: CBTExercise }) {
  const { toast } = useToast();
  const [journalText, setJournalText] = useState('');
  const IconComponent = getIcon(exercise.iconName);

  const handleStartExercise = () => {
    if (!exercise.interactive) {
      toast({
        title: "Exercise Information",
        description: `Read more about "${exercise.title}" to practice this technique. Interactive component coming soon.`,
      });
    }
    // For interactive exercises, the DialogTrigger will open the modal.
  };

  const handleSaveJournal = () => {
     toast({
        title: "Journal Saved (Simulated)",
        description: `Your entry for "${exercise.title}" has been saved. Content: ${journalText.substring(0,50)}...`,
      });
      setJournalText(''); // Clear textarea
      // Here you would typically save to context or backend
  }

  return (
    <Card className="shadow-md hover:shadow-lg transition-shadow w-full flex flex-col">
      <CardHeader>
        <div className="flex items-center gap-3 mb-2">
          <IconComponent className="h-8 w-8 text-primary" />
          <CardTitle className="text-xl">{exercise.title}</CardTitle>
        </div>
        <CardDescription className="text-sm text-muted-foreground">{exercise.category}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <p className="text-muted-foreground text-sm">{exercise.description}</p>
      </CardContent>
      <CardFooter>
        {exercise.interactive ? (
          <Dialog>
            <DialogTrigger asChild>
              <Button className="w-full">
                <LucideIcons.PlayCircle className="mr-2 h-4 w-4" /> Start Exercise
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2"><IconComponent className="h-6 w-6 text-primary" /> {exercise.title}</DialogTitle>
                <DialogDescription>{exercise.description}</DialogDescription>
              </DialogHeader>
              <div className="py-4 space-y-3">
                <Label htmlFor={`exercise-input-${exercise.id}`}>Your thoughts/notes:</Label>
                <Textarea
                  id={`exercise-input-${exercise.id}`}
                  placeholder={exercise.title === 'Gratitude Journaling Prompt' ? "e.g., I'm grateful for my morning coffee because..." : "Write your thoughts here..."}
                  rows={5}
                  value={journalText}
                  onChange={(e) => setJournalText(e.target.value)}
                />
                {/* Add more specific interactive elements here based on exercise type in future */}
              </div>
              <DialogFooter className="sm:justify-between">
                 <DialogClose asChild>
                    <Button type="button" variant="outline">Cancel</Button>
                 </DialogClose>
                 <DialogClose asChild>
                    <Button type="button" onClick={handleSaveJournal} disabled={!journalText.trim()}>Save Journal</Button>
                 </DialogClose>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        ) : (
          <Button className="w-full" variant="outline" onClick={handleStartExercise}>
            <LucideIcons.Info className="mr-2 h-4 w-4" /> Learn More
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}

export function CbtExercises() {
  return (
    <div className="space-y-8">
       <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl flex items-center gap-2"><LucideIcons.BrainCog className="h-7 w-7 text-primary"/>Self-Guided CBT Exercises</CardTitle>
          <CardDescription>Practice Cognitive Behavioral Therapy techniques. Interactive exercises will open a dialog.</CardDescription>
        </CardHeader>
      </Card>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {PLACEHOLDER_CBT_EXERCISES.map((exercise) => (
          <ExerciseCard key={exercise.id} exercise={exercise} />
        ))}
      </div>
    </div>
  );
}
