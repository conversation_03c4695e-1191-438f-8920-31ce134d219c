import { UserRole, UserStatus, PaymentType, MessageType } from '@prisma/client';
export interface AuthenticatedUser {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    emailVerified: boolean;
    twoFactorEnabled: boolean;
}
export interface JWTPayload {
    userId: string;
    email: string;
    role: UserRole;
    iat?: number;
    exp?: number;
}
export interface LoginRequest {
    email: string;
    password: string;
    twoFactorCode?: string;
}
export interface RegisterRequest {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    phoneNumber?: string;
    dateOfBirth?: string;
}
export interface AuthResponse {
    user: AuthenticatedUser;
    accessToken: string;
    refreshToken: string;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginationParams {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface SessionCreateRequest {
    psychiatristId: string;
    scheduledAt: string;
    duration?: number;
    notes?: string;
}
export interface MessageCreateRequest {
    receiverId?: string;
    sessionId?: string;
    content: string;
    messageType?: MessageType;
    fileUrl?: string;
    fileName?: string;
}
export interface ForumPostCreateRequest {
    title: string;
    content: string;
    category: string;
    tags?: string[];
    isAnonymous?: boolean;
}
export interface ForumCommentCreateRequest {
    postId: string;
    content: string;
    parentId?: string;
    isAnonymous?: boolean;
}
export interface PaymentCreateRequest {
    amount: number;
    paymentType: PaymentType;
    sessionId?: string;
    description?: string;
}
export interface PsychiatristProfileUpdateRequest {
    specialties?: string[];
    bio?: string;
    education?: string;
    experience?: string;
    languages?: string[];
    hourlyRate?: number;
    acceptsInsurance?: boolean;
}
export interface AvailabilitySlot {
    dayOfWeek: number;
    startTime: string;
    endTime: string;
    isAvailable: boolean;
}
export interface CrisisDetectionResult {
    isHighRisk: boolean;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    confidence: number;
    keywords: string[];
    suggestions: string[];
}
export interface AIAnalysisRequest {
    content: string;
    type: 'journal' | 'forum_post' | 'message';
    userId: string;
}
export interface AIAnalysisResponse {
    sentiment: 'positive' | 'negative' | 'neutral' | 'concerning';
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    suggestions: string[];
    confidence: number;
    keywords: string[];
}
export interface SocketUser {
    userId: string;
    socketId: string;
    role: UserRole;
    isOnline: boolean;
    lastSeen: Date;
}
export interface ChatMessage {
    id: string;
    senderId: string;
    receiverId?: string;
    sessionId?: string;
    content: string;
    messageType: MessageType;
    timestamp: Date;
    isRead: boolean;
}
export interface NotificationData {
    type: 'session_reminder' | 'new_message' | 'payment_success' | 'session_approved' | 'crisis_alert';
    title: string;
    message: string;
    data?: any;
}
export interface EmailTemplate {
    to: string;
    subject: string;
    html: string;
    text?: string;
}
export interface StripePaymentIntent {
    id: string;
    amount: number;
    currency: string;
    status: string;
    clientSecret: string;
}
export interface JitsiMeetConfig {
    roomName: string;
    jwt?: string;
    userInfo: {
        displayName: string;
        email: string;
    };
    configOverwrite?: any;
    interfaceConfigOverwrite?: any;
}
export interface SystemStats {
    totalUsers: number;
    totalSessions: number;
    totalRevenue: number;
    activeUsers: number;
    pendingApprovals: number;
    flaggedContent: number;
}
export interface ModerationActionRequest {
    targetUserId?: string;
    targetPostId?: string;
    targetMessageId?: string;
    action: string;
    reason: string;
    duration?: number;
    notes?: string;
}
export interface FileUploadResult {
    filename: string;
    originalName: string;
    size: number;
    mimetype: string;
    url: string;
}
//# sourceMappingURL=index.d.ts.map