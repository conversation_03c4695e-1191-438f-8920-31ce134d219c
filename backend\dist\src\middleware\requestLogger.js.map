{"version": 3, "file": "requestLogger.js", "sourceRoot": "", "sources": ["../../../src/middleware/requestLogger.ts"], "names": [], "mappings": ";;;AAEO,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC/E,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAGzB,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IAGtF,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;QACpC,MAAM,WAAW,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC;QACpE,MAAM,UAAU,GAAG,SAAS,CAAC;QAE7B,OAAO,CAAC,GAAG,CACT,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,KAAK;YAC3D,GAAG,WAAW,GAAG,GAAG,CAAC,UAAU,GAAG,UAAU,MAAM,QAAQ,IAAI,CAC/D,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAnBW,QAAA,aAAa,iBAmBxB"}