import React from 'react';

export function Logo(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 100 100"
      width="40"
      height="40"
      aria-label="WELL Logo"
      {...props}
    >
      <defs>
        <linearGradient id="petalGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style={{ stopColor: 'hsl(var(--primary))', stopOpacity: 1 }} />
          <stop offset="100%" style={{ stopColor: 'hsl(var(--accent))', stopOpacity: 0.8 }} />
        </linearGradient>
        <linearGradient id="leafGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style={{ stopColor: 'hsl(var(--secondary))', stopOpacity: 1 }} />
          <stop offset="100%" style={{ stopColor: 'hsl(var(--secondary) / 0.7)', stopOpacity: 1 }} />
        </linearGradient>
      </defs>
      
      {/* Simplified flower/bloom design */}
      {/* Petals */}
      {[0, 72, 144, 216, 288].map(angle => (
        <ellipse
          key={angle}
          cx="50"
          cy="50"
          rx="15"
          ry="30"
          fill="url(#petalGradient)"
          transform={`rotate(${angle} 50 50) translate(0 -10)`}
          opacity="0.8"
        />
      ))}
      
      {/* Center */}
      <circle cx="50" cy="50" r="12" fill="hsl(var(--accent))" />
      <circle cx="50" cy="50" r="8" fill="hsl(var(--background))" />
      <circle cx="50" cy="50" r="5" fill="hsl(var(--accent) / 0.7)" />

      {/* Optional subtle leaves */}
       <path 
        d="M 50 80 Q 40 90 30 85 C 35 75 45 70 50 80 Z"
        fill="url(#leafGradient)"
        transform="rotate(20 50 50)"
        opacity="0.7"
      />
      <path 
        d="M 50 80 Q 60 90 70 85 C 65 75 55 70 50 80 Z"
        fill="url(#leafGradient)"
        transform="rotate(-20 50 50)"
        opacity="0.7"
      />
    </svg>
  );
}

