'use server';

/**
 * @fileOverview Provides insights into mood patterns and potential triggers from journal entries.
 *
 * - `getJournalInsights` - A function that analyzes journal entries and provides insights.
 * - `JournalInsightsInput` - The input type for the `getJournalInsights` function.
 * - `JournalInsightsOutput` - The return type for the `getJournalInsights` function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const JournalInsightsInputSchema = z.object({
  journalEntries: z
    .string()
    .describe('A string containing all journal entries from the user.'),
});
export type JournalInsightsInput = z.infer<typeof JournalInsightsInputSchema>;

const JournalInsightsOutputSchema = z.object({
  moodPatterns: z
    .string()
    .describe('A summary of the identified mood patterns in the journal entries.'),
  potentialTriggers: z
    .string()
    .describe('A list of potential triggers identified in the journal entries.'),
  overallSentiment: z
    .string()
    .describe('The overall sentiment expressed in the journal entries (e.g., positive, negative, mixed).'),
});
export type JournalInsightsOutput = z.infer<typeof JournalInsightsOutputSchema>;

export async function getJournalInsights(input: JournalInsightsInput): Promise<JournalInsightsOutput> {
  return journalInsightsFlow(input);
}

const journalInsightsPrompt = ai.definePrompt({
  name: 'journalInsightsPrompt',
  input: {schema: JournalInsightsInputSchema},
  output: {schema: JournalInsightsOutputSchema},
  prompt: `You are a mental health expert analyzing a user's journal entries to provide insights into their mood patterns and potential triggers.

  Analyze the following journal entries and identify mood patterns, potential triggers, and the overall sentiment.

  Journal Entries: {{{journalEntries}}}

  Provide the mood patterns, potential triggers, and overall sentiment based on your analysis of the journal entries.
  `,
});

const journalInsightsFlow = ai.defineFlow(
  {
    name: 'journalInsightsFlow',
    inputSchema: JournalInsightsInputSchema,
    outputSchema: JournalInsightsOutputSchema,
  },
  async input => {
    const {output} = await journalInsightsPrompt(input);
    return output!;
  }
);
