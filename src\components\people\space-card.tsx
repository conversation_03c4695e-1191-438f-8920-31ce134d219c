
'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import type { Space } from '@/lib/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Tag, ExternalLink, CreditCard } from 'lucide-react';

interface SpaceCardProps {
  space: Space;
}

export function SpaceCard({ space }: SpaceCardProps) {
  const hostInitials = space.host.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'H';

  return (
    <Card className="flex flex-col h-full shadow-lg hover:shadow-xl transition-shadow duration-300">
      <CardHeader className="p-0">
        <div className="relative aspect-[16/6] w-full">
          <Image
            src={space.bannerImageUrl}
            alt={`${space.name} banner`}
            layout="fill"
            objectFit="cover"
            className="rounded-t-lg"
            data-ai-hint={space.aiBannerHint || 'community banner'}
          />
        </div>
        <div className="flex items-end p-4 -mt-10">
            <Avatar className="h-20 w-20 border-4 border-background bg-background shadow-md">
                <AvatarImage src={space.profileImageUrl} alt={space.name} data-ai-hint={space.aiProfileHint || 'space logo'} />
                <AvatarFallback>{space.name.charAt(0).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div className="ml-4">
                <CardTitle className="text-xl font-bold">{space.name}</CardTitle>
            </div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow pt-0 px-4 pb-4">
        <div className="flex items-center text-sm text-muted-foreground mb-2">
          <Avatar className="h-6 w-6 mr-2">
            <AvatarImage src={space.host.avatarUrl} alt={space.host.name} data-ai-hint={space.host.aiHostAvatarHint || 'host photo'} />
            <AvatarFallback>{hostInitials}</AvatarFallback>
          </Avatar>
          Hosted by {space.host.name}
        </div>
        <CardDescription className="text-sm text-foreground/80 mb-3 line-clamp-3 min-h-[3.75rem]">
          {space.description}
        </CardDescription>
        <div className="flex flex-wrap gap-1 mb-3">
          {space.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="secondary" className="text-xs">
              <Tag className="mr-1 h-3 w-3" /> {tag}
            </Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter className="px-4 pb-4 flex flex-col sm:flex-row justify-between items-center gap-2">
        <div className="flex items-center text-sm text-muted-foreground">
          <Users className="mr-1.5 h-4 w-4" />
          {space.followerCount.toLocaleString()} followers
        </div>
        <div className="flex items-center gap-2">
        {space.isSubscriptionBased ? (
            <Badge variant="outline" className="border-accent text-accent">
                <CreditCard className="mr-1.5 h-4 w-4" /> Paid
            </Badge>
            ) : (
            <Badge variant="outline" className="border-green-500 text-green-600 dark:text-green-400">
                Free
            </Badge>
        )}
        <Button asChild size="sm">
          <Link href={`/people/${space.id}`}>
            View Space <ExternalLink className="ml-1.5 h-4 w-4" />
          </Link>
        </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
