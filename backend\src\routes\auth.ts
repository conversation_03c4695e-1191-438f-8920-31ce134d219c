import { Router } from 'express';
import { z } from 'zod';
import { UserRole } from '@prisma/client';
import { prisma } from '../../server';
import { AuthUtils } from '../utils/auth';
import { emailService } from '../utils/email';
import { async<PERSON>and<PERSON>, CustomError } from '../middleware/errorHandler';
import { authMiddleware } from '../middleware/auth';
import { LoginRequest, RegisterRequest } from '../types';

const router = Router();

// Validation schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  role: z.enum([UserRole.PATIENT, UserRole.PSYCHIATRIST]),
  phoneNumber: z.string().optional(),
  dateOfBirth: z.string().optional(),
});

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  twoFactorCode: z.string().optional(),
});

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

const verifyEmailSchema = z.object({
  token: z.string().min(1, 'Verification token is required'),
});

const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

// Register endpoint
router.post('/register', asyncHandler(async (req, res) => {
  const validatedData = registerSchema.parse(req.body);
  
  // Validate password strength
  const passwordValidation = AuthUtils.validatePassword(validatedData.password);
  if (!passwordValidation.isValid) {
    throw new CustomError(passwordValidation.errors.join(', '), 400);
  }

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: validatedData.email.toLowerCase() },
  });

  if (existingUser) {
    throw new CustomError('User with this email already exists', 409);
  }

  // Hash password
  const hashedPassword = await AuthUtils.hashPassword(validatedData.password);
  
  // Generate email verification token
  const emailVerificationToken = AuthUtils.generateEmailVerificationToken();

  // Create user
  const user = await prisma.user.create({
    data: {
      email: validatedData.email.toLowerCase(),
      password: hashedPassword,
      firstName: validatedData.firstName,
      lastName: validatedData.lastName,
      role: validatedData.role,
      phoneNumber: validatedData.phoneNumber,
      dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : null,
      emailVerificationToken,
      status: validatedData.role === UserRole.PSYCHIATRIST ? 'PENDING_APPROVAL' : 'PENDING',
    },
  });

  // Create role-specific profile
  if (validatedData.role === UserRole.PATIENT) {
    await prisma.patientProfile.create({
      data: { userId: user.id },
    });
  } else if (validatedData.role === UserRole.PSYCHIATRIST) {
    await prisma.psychiatristProfile.create({
      data: {
        userId: user.id,
        licenseNumber: '', // Will be filled during verification
        specialties: '[]',
        hourlyRate: 0,
      },
    });
  }

  // Send verification email
  try {
    await emailService.sendEmailVerification(
      user.email,
      emailVerificationToken,
      user.firstName
    );
  } catch (error) {
    console.error('Failed to send verification email:', error);
    // Don't fail registration if email fails
  }

  res.status(201).json({
    success: true,
    message: 'Registration successful. Please check your email for verification.',
    data: {
      userId: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
    },
  });
}));

// Login endpoint
router.post('/login', asyncHandler(async (req, res) => {
  const { email, password, twoFactorCode } = loginSchema.parse(req.body);

  // Find user
  const user = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
  });

  if (!user) {
    throw new CustomError('Invalid credentials', 401);
  }

  // Check password
  const isPasswordValid = await AuthUtils.comparePassword(password, user.password);
  if (!isPasswordValid) {
    throw new CustomError('Invalid credentials', 401);
  }

  // Check account status
  if (user.status === 'SUSPENDED') {
    throw new CustomError('Account suspended', 403);
  }

  if (user.status === 'BANNED') {
    throw new CustomError('Account banned', 403);
  }

  // Check 2FA if enabled
  if (user.twoFactorEnabled) {
    if (!twoFactorCode) {
      throw new CustomError('Two-factor authentication code required', 400);
    }

    if (!user.twoFactorSecret) {
      throw new CustomError('Two-factor authentication not properly configured', 500);
    }

    const isValidTwoFactor = AuthUtils.verifyTwoFactorToken(twoFactorCode, user.twoFactorSecret);
    if (!isValidTwoFactor) {
      throw new CustomError('Invalid two-factor authentication code', 401);
    }
  }

  // Update last login
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLoginAt: new Date() },
  });

  // Create auth response
  const authUser = AuthUtils.sanitizeUserData(user);
  const authResponse = AuthUtils.createAuthResponse(authUser);

  res.json({
    success: true,
    message: 'Login successful',
    data: authResponse,
  });
}));

// Verify email endpoint
router.post('/verify-email', asyncHandler(async (req, res) => {
  const { token } = verifyEmailSchema.parse(req.body);

  const user = await prisma.user.findFirst({
    where: { emailVerificationToken: token },
  });

  if (!user) {
    throw new CustomError('Invalid or expired verification token', 400);
  }

  await prisma.user.update({
    where: { id: user.id },
    data: {
      emailVerified: true,
      emailVerificationToken: null,
      status: user.role === UserRole.PSYCHIATRIST ? 'PENDING_APPROVAL' : 'ACTIVE',
    },
  });

  res.json({
    success: true,
    message: 'Email verified successfully',
  });
}));

// Forgot password endpoint
router.post('/forgot-password', asyncHandler(async (req, res) => {
  const { email } = forgotPasswordSchema.parse(req.body);

  const user = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
  });

  if (!user) {
    // Don't reveal if email exists
    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.',
    });
    return;
  }

  const resetToken = AuthUtils.generatePasswordResetToken();
  const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

  await prisma.user.update({
    where: { id: user.id },
    data: {
      passwordResetToken: resetToken,
      passwordResetExpires: resetExpires,
    },
  });

  try {
    await emailService.sendPasswordReset(user.email, resetToken, user.firstName);
  } catch (error) {
    console.error('Failed to send password reset email:', error);
    throw new CustomError('Failed to send password reset email', 500);
  }

  res.json({
    success: true,
    message: 'Password reset link sent to your email',
  });
}));

// Reset password endpoint
router.post('/reset-password', asyncHandler(async (req, res) => {
  const { token, password } = resetPasswordSchema.parse(req.body);

  // Validate password strength
  const passwordValidation = AuthUtils.validatePassword(password);
  if (!passwordValidation.isValid) {
    throw new CustomError(passwordValidation.errors.join(', '), 400);
  }

  const user = await prisma.user.findFirst({
    where: {
      passwordResetToken: token,
      passwordResetExpires: {
        gt: new Date(),
      },
    },
  });

  if (!user) {
    throw new CustomError('Invalid or expired reset token', 400);
  }

  const hashedPassword = await AuthUtils.hashPassword(password);

  await prisma.user.update({
    where: { id: user.id },
    data: {
      password: hashedPassword,
      passwordResetToken: null,
      passwordResetExpires: null,
    },
  });

  res.json({
    success: true,
    message: 'Password reset successful',
  });
}));

// Refresh token endpoint
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = refreshTokenSchema.parse(req.body);

  try {
    const decoded = AuthUtils.verifyRefreshToken(refreshToken);
    
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        emailVerified: true,
        twoFactorEnabled: true,
      },
    });

    if (!user || user.status === 'SUSPENDED' || user.status === 'BANNED') {
      throw new CustomError('Invalid refresh token', 401);
    }

    const authUser = AuthUtils.sanitizeUserData(user);
    const tokens = AuthUtils.generateTokenPair(authUser);

    res.json({
      success: true,
      data: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
      },
    });
  } catch (error) {
    throw new CustomError('Invalid refresh token', 401);
  }
}));

// Get current user endpoint
router.get('/me', authMiddleware, asyncHandler(async (req, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      emailVerified: true,
      twoFactorEnabled: true,
      profileImage: true,
      phoneNumber: true,
      dateOfBirth: true,
      timezone: true,
      createdAt: true,
      lastLoginAt: true,
    },
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  res.json({
    success: true,
    data: user,
  });
}));

// Logout endpoint
router.post('/logout', authMiddleware, asyncHandler(async (req, res) => {
  // In a more sophisticated implementation, you might want to blacklist the token
  // For now, we'll just return success as the client will remove the token
  
  res.json({
    success: true,
    message: 'Logged out successfully',
  });
}));

export default router;
