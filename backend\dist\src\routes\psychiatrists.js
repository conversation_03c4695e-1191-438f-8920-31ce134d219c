"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
const server_1 = require("../../server");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const updateProfileSchema = zod_1.z.object({
    specialties: zod_1.z.array(zod_1.z.string()).optional(),
    bio: zod_1.z.string().max(2000).optional(),
    education: zod_1.z.array(zod_1.z.string()).optional(),
    experience: zod_1.z.string().max(1000).optional(),
    languages: zod_1.z.array(zod_1.z.string()).optional(),
    hourlyRate: zod_1.z.number().min(0).max(1000).optional(),
    acceptsInsurance: zod_1.z.boolean().optional(),
});
const availabilitySchema = zod_1.z.object({
    availability: zod_1.z.array(zod_1.z.object({
        dayOfWeek: zod_1.z.number().min(0).max(6),
        startTime: zod_1.z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
        endTime: zod_1.z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
        isAvailable: zod_1.z.boolean(),
    })),
});
router.get('/', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const query = zod_1.z.object({
        page: zod_1.z.string().transform(Number).optional().default(1),
        limit: zod_1.z.string().transform(Number).optional().default(20),
        specialty: zod_1.z.string().optional(),
        minRate: zod_1.z.string().transform(Number).optional(),
        maxRate: zod_1.z.string().transform(Number).optional(),
        acceptsInsurance: zod_1.z.string().transform(val => val === 'true').optional(),
        search: zod_1.z.string().optional(),
    }).parse(req.query);
    const where = {
        role: client_1.UserRole.PSYCHIATRIST,
        status: 'ACTIVE',
        psychiatristProfile: {
            verified: true,
        },
    };
    const profileWhere = { verified: true };
    if (query.specialty) {
        profileWhere.specialties = {
            contains: query.specialty,
        };
    }
    if (query.minRate !== undefined || query.maxRate !== undefined) {
        profileWhere.hourlyRate = {};
        if (query.minRate !== undefined) {
            profileWhere.hourlyRate.gte = query.minRate;
        }
        if (query.maxRate !== undefined) {
            profileWhere.hourlyRate.lte = query.maxRate;
        }
    }
    if (query.acceptsInsurance !== undefined) {
        profileWhere.acceptsInsurance = query.acceptsInsurance;
    }
    if (query.search) {
        where.OR = [
            { firstName: { contains: query.search } },
            { lastName: { contains: query.search } },
            { psychiatristProfile: { bio: { contains: query.search } } },
        ];
    }
    where.psychiatristProfile = profileWhere;
    const [psychiatrists, total] = await Promise.all([
        server_1.prisma.user.findMany({
            where,
            select: {
                id: true,
                firstName: true,
                lastName: true,
                profileImage: true,
                psychiatristProfile: {
                    select: {
                        specialties: true,
                        bio: true,
                        education: true,
                        experience: true,
                        languages: true,
                        hourlyRate: true,
                        acceptsInsurance: true,
                        verified: true,
                    },
                },
                _count: {
                    select: {
                        psychiatristSessions: {
                            where: { status: 'COMPLETED' },
                        },
                        receivedReviews: true,
                    },
                },
            },
            orderBy: { createdAt: 'desc' },
            skip: (query.page - 1) * query.limit,
            take: query.limit,
        }),
        server_1.prisma.user.count({ where }),
    ]);
    const psychiatristsWithRatings = await Promise.all(psychiatrists.map(async (psychiatrist) => {
        const avgRating = await server_1.prisma.review.aggregate({
            where: { targetId: psychiatrist.id },
            _avg: { rating: true },
        });
        return {
            ...psychiatrist,
            psychiatristProfile: psychiatrist.psychiatristProfile ? {
                ...psychiatrist.psychiatristProfile,
                specialties: psychiatrist.psychiatristProfile.specialties
                    ? JSON.parse(psychiatrist.psychiatristProfile.specialties)
                    : [],
                education: psychiatrist.psychiatristProfile.education
                    ? JSON.parse(psychiatrist.psychiatristProfile.education)
                    : [],
                languages: psychiatrist.psychiatristProfile.languages
                    ? JSON.parse(psychiatrist.psychiatristProfile.languages)
                    : [],
            } : null,
            stats: {
                completedSessions: psychiatrist._count.psychiatristSessions,
                totalReviews: psychiatrist._count.receivedReviews,
                averageRating: avgRating._avg.rating || 0,
            },
        };
    }));
    res.json({
        success: true,
        data: {
            psychiatrists: psychiatristsWithRatings,
            pagination: {
                page: query.page,
                limit: query.limit,
                total,
                totalPages: Math.ceil(total / query.limit),
                hasNext: query.page * query.limit < total,
                hasPrev: query.page > 1,
            },
        },
    });
}));
router.get('/:psychiatristId', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { psychiatristId } = req.params;
    const psychiatrist = await server_1.prisma.user.findUnique({
        where: {
            id: psychiatristId,
            role: client_1.UserRole.PSYCHIATRIST,
            status: 'ACTIVE',
        },
        include: {
            psychiatristProfile: {
                include: {
                    availability: true,
                },
            },
            receivedReviews: {
                where: { isApproved: true },
                include: {
                    author: {
                        select: {
                            firstName: true,
                            lastName: true,
                            profileImage: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
                take: 10,
            },
            _count: {
                select: {
                    psychiatristSessions: {
                        where: { status: 'COMPLETED' },
                    },
                    receivedReviews: {
                        where: { isApproved: true },
                    },
                },
            },
        },
    });
    if (!psychiatrist || !psychiatrist.psychiatristProfile?.verified) {
        throw new errorHandler_1.CustomError('Psychiatrist not found', 404);
    }
    const avgRating = await server_1.prisma.review.aggregate({
        where: { targetId: psychiatristId, isApproved: true },
        _avg: { rating: true },
    });
    const psychiatristData = {
        ...psychiatrist,
        psychiatristProfile: {
            ...psychiatrist.psychiatristProfile,
            specialties: psychiatrist.psychiatristProfile.specialties
                ? JSON.parse(psychiatrist.psychiatristProfile.specialties)
                : [],
            education: psychiatrist.psychiatristProfile.education
                ? JSON.parse(psychiatrist.psychiatristProfile.education)
                : [],
            languages: psychiatrist.psychiatristProfile.languages
                ? JSON.parse(psychiatrist.psychiatristProfile.languages)
                : [],
        },
        stats: {
            completedSessions: psychiatrist._count.psychiatristSessions,
            totalReviews: psychiatrist._count.receivedReviews,
            averageRating: avgRating._avg.rating || 0,
        },
        reviews: psychiatrist.receivedReviews.map(review => ({
            ...review,
            author: review.isAnonymous ? null : review.author,
        })),
    };
    res.json({
        success: true,
        data: psychiatristData,
    });
}));
router.put('/profile', auth_1.requireEmailVerification, (0, auth_1.requireRole)([client_1.UserRole.PSYCHIATRIST]), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = updateProfileSchema.parse(req.body);
    const userId = req.user.id;
    const updateData = {};
    if (validatedData.specialties) {
        updateData.specialties = JSON.stringify(validatedData.specialties);
    }
    if (validatedData.bio)
        updateData.bio = validatedData.bio;
    if (validatedData.education) {
        updateData.education = JSON.stringify(validatedData.education);
    }
    if (validatedData.experience)
        updateData.experience = validatedData.experience;
    if (validatedData.languages) {
        updateData.languages = JSON.stringify(validatedData.languages);
    }
    if (validatedData.hourlyRate !== undefined)
        updateData.hourlyRate = validatedData.hourlyRate;
    if (validatedData.acceptsInsurance !== undefined)
        updateData.acceptsInsurance = validatedData.acceptsInsurance;
    const profile = await server_1.prisma.psychiatristProfile.update({
        where: { userId },
        data: updateData,
    });
    res.json({
        success: true,
        message: 'Profile updated successfully',
        data: {
            ...profile,
            specialties: profile.specialties ? JSON.parse(profile.specialties) : [],
            education: profile.education ? JSON.parse(profile.education) : [],
            languages: profile.languages ? JSON.parse(profile.languages) : [],
        },
    });
}));
router.put('/availability', auth_1.requireEmailVerification, (0, auth_1.requireRole)([client_1.UserRole.PSYCHIATRIST]), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { availability } = availabilitySchema.parse(req.body);
    const userId = req.user.id;
    const profile = await server_1.prisma.psychiatristProfile.findUnique({
        where: { userId },
    });
    if (!profile) {
        throw new errorHandler_1.CustomError('Psychiatrist profile not found', 404);
    }
    await server_1.prisma.availability.deleteMany({
        where: { psychiatristId: profile.id },
    });
    const availabilityData = availability.map(slot => ({
        psychiatristId: profile.id,
        ...slot,
    }));
    await server_1.prisma.availability.createMany({
        data: availabilityData,
    });
    res.json({
        success: true,
        message: 'Availability updated successfully',
    });
}));
router.get('/:psychiatristId/availability', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { psychiatristId } = req.params;
    const profile = await server_1.prisma.psychiatristProfile.findUnique({
        where: { userId: psychiatristId },
        include: { availability: true },
    });
    if (!profile) {
        throw new errorHandler_1.CustomError('Psychiatrist not found', 404);
    }
    res.json({
        success: true,
        data: profile.availability,
    });
}));
exports.default = router;
//# sourceMappingURL=psychiatrists.js.map