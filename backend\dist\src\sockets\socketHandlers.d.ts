import { Server as SocketIOServer } from 'socket.io';
import { SocketUser, NotificationData } from '../types';
export declare const initializeSocketHandlers: (io: SocketIOServer) => {
    sendNotificationToUser: (userId: string, notification: NotificationData) => void;
    getOnlineUsers: () => SocketUser[];
    isUserOnline: (userId: string) => boolean;
};
//# sourceMappingURL=socketHandlers.d.ts.map