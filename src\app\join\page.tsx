
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Stethoscope, ArrowRight } from 'lucide-react';
import { Logo } from '@/components/icons/logo';
import { APP_NAME } from '@/lib/constants';

export default function JoinOptionsPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-background to-secondary/10 p-4">
      <div className="flex items-center mb-12">
        <Logo className="h-12 w-12 mr-3 text-primary" />
        <h1 className="text-4xl font-bold text-primary">{APP_NAME}</h1>
      </div>
      <Card className="w-full max-w-xl shadow-2xl text-center">
        <CardHeader>
          <CardTitle className="text-3xl font-semibold">Welcome to {APP_NAME}!</CardTitle>
          <CardDescription className="text-lg text-muted-foreground pt-2">
            How would you like to join our community today?
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 pt-4">
          <Link href="/journal" className="block">
            <Button
              variant="default"
              className="w-full h-auto py-6 text-lg shadow-lg hover:shadow-xl transition-shadow group"
            >
              <User className="mr-3 h-7 w-7 transition-transform group-hover:scale-110" />
              <div className="text-left">
                <span className="font-semibold block">I'm Seeking Wellness Support</span>
                <span className="text-sm opacity-90 block">Access journaling, insights, resources, and community.</span>
              </div>
              <ArrowRight className="ml-auto h-6 w-6 opacity-70 group-hover:opacity-100 transition-opacity" />
            </Button>
          </Link>
          <Link href="/therapist-login" className="block"> {/* Updated link */}
            <Button
              variant="outline"
              className="w-full h-auto py-6 text-lg shadow-lg hover:shadow-xl transition-shadow group"
            >
              <Stethoscope className="mr-3 h-7 w-7 transition-transform group-hover:scale-110" />
               <div className="text-left">
                <span className="font-semibold block">I'm a Wellness Professional</span>
                <span className="text-sm opacity-90 block">Access your therapist dashboard and tools.</span>
              </div>
              <ArrowRight className="ml-auto h-6 w-6 opacity-70 group-hover:opacity-100 transition-opacity" />
            </Button>
          </Link>
        </CardContent>
      </Card>
      <p className="mt-12 text-center text-muted-foreground">
        <Link href="/" className="hover:text-primary underline">
          Back to Homepage
        </Link>
      </p>
    </div>
  );
}
