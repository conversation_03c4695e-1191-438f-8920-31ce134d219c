"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const auth_1 = require("../src/utils/auth");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seeding...');
    const adminPassword = await auth_1.AuthUtils.hashPassword(process.env.ADMIN_PASSWORD || 'Admin123!@#');
    const admin = await prisma.user.upsert({
        where: { email: process.env.ADMIN_EMAIL || '<EMAIL>' },
        update: {},
        create: {
            email: process.env.ADMIN_EMAIL || '<EMAIL>',
            password: adminPassword,
            firstName: 'System',
            lastName: 'Administrator',
            role: client_1.UserRole.ADMIN,
            status: client_1.UserStatus.ACTIVE,
            emailVerified: true,
        },
    });
    console.log('✅ Admin user created:', admin.email);
    const moderatorPassword = await auth_1.AuthUtils.hashPassword('Moderator123!@#');
    const moderator = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            password: moderatorPassword,
            firstName: 'John',
            lastName: 'Moderator',
            role: client_1.UserRole.MODERATOR,
            status: client_1.UserStatus.ACTIVE,
            emailVerified: true,
        },
    });
    console.log('✅ Moderator user created:', moderator.email);
    const psychiatristPassword = await auth_1.AuthUtils.hashPassword('Psychiatrist123!@#');
    const psychiatrist = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            password: psychiatristPassword,
            firstName: 'Dr. Sarah',
            lastName: 'Johnson',
            role: client_1.UserRole.PSYCHIATRIST,
            status: client_1.UserStatus.ACTIVE,
            emailVerified: true,
        },
    });
    await prisma.psychiatristProfile.upsert({
        where: { userId: psychiatrist.id },
        update: {},
        create: {
            userId: psychiatrist.id,
            licenseNumber: 'PSY123456',
            specialties: JSON.stringify(['Anxiety', 'Depression', 'PTSD', 'Cognitive Behavioral Therapy']),
            bio: 'Dr. Sarah Johnson is a licensed psychiatrist with over 10 years of experience in treating anxiety, depression, and trauma-related disorders. She specializes in cognitive behavioral therapy and mindfulness-based interventions.',
            education: JSON.stringify([
                'MD - Harvard Medical School',
                'Residency in Psychiatry - Johns Hopkins Hospital',
                'Fellowship in Trauma and PTSD - Mayo Clinic'
            ]),
            experience: 'Over 10 years of clinical experience in mental health treatment',
            languages: JSON.stringify(['English', 'Spanish']),
            hourlyRate: 150.00,
            acceptsInsurance: true,
            verified: true,
        },
    });
    const availabilitySlots = [
        { dayOfWeek: 1, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 2, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 3, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 4, startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 5, startTime: '09:00', endTime: '15:00' },
    ];
    const psychiatristProfile = await prisma.psychiatristProfile.findUnique({
        where: { userId: psychiatrist.id },
    });
    if (psychiatristProfile) {
        for (const slot of availabilitySlots) {
            await prisma.availability.upsert({
                where: {
                    psychiatristId_dayOfWeek: {
                        psychiatristId: psychiatristProfile.id,
                        dayOfWeek: slot.dayOfWeek,
                    },
                },
                update: {},
                create: {
                    psychiatristId: psychiatristProfile.id,
                    ...slot,
                },
            });
        }
    }
    console.log('✅ Psychiatrist user and profile created:', psychiatrist.email);
    const patientPassword = await auth_1.AuthUtils.hashPassword('Patient123!@#');
    const patient = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            password: patientPassword,
            firstName: 'Alice',
            lastName: 'Smith',
            role: client_1.UserRole.PATIENT,
            status: client_1.UserStatus.ACTIVE,
            emailVerified: true,
            dateOfBirth: new Date('1990-05-15'),
            phoneNumber: '+**********',
        },
    });
    await prisma.patientProfile.upsert({
        where: { userId: patient.id },
        update: {},
        create: {
            userId: patient.id,
            emergencyContact: 'John Smith - +********** (Husband)',
            medicalHistory: 'History of anxiety and mild depression. No major medical conditions.',
            currentMedications: 'Sertraline 50mg daily',
            allergies: 'No known allergies',
            preferredLanguages: 'English',
            consentToAI: true,
        },
    });
    console.log('✅ Patient user and profile created:', patient.email);
    const crisisResources = [
        {
            name: 'National Suicide Prevention Lifeline',
            description: '24/7 free and confidential support for people in distress, prevention and crisis resources.',
            phoneNumber: '988',
            website: 'https://suicidepreventionlifeline.org',
            region: 'US',
            category: 'suicide_prevention',
        },
        {
            name: 'Crisis Text Line',
            description: 'Free, 24/7 support for those in crisis. Text HOME to 741741.',
            phoneNumber: '741741',
            website: 'https://www.crisistextline.org',
            region: 'US',
            category: 'crisis_hotline',
        },
        {
            name: 'SAMHSA National Helpline',
            description: 'Treatment referral and information service for mental health and substance use disorders.',
            phoneNumber: '**************',
            website: 'https://www.samhsa.gov/find-help/national-helpline',
            region: 'US',
            category: 'mental_health',
        },
    ];
    for (const resource of crisisResources) {
        await prisma.crisisResource.upsert({
            where: { name: resource.name },
            update: {},
            create: resource,
        });
    }
    console.log('✅ Crisis resources created');
    const systemSettings = [
        {
            key: 'platform_name',
            value: 'WELL Platform',
            description: 'The name of the platform',
        },
        {
            key: 'session_duration_default',
            value: '60',
            description: 'Default session duration in minutes',
        },
        {
            key: 'session_buffer_time',
            value: '15',
            description: 'Buffer time between sessions in minutes',
        },
        {
            key: 'max_file_size',
            value: '10485760',
            description: 'Maximum file upload size in bytes (10MB)',
        },
        {
            key: 'ai_moderation_enabled',
            value: 'true',
            description: 'Enable AI-powered content moderation',
        },
        {
            key: 'crisis_detection_enabled',
            value: 'true',
            description: 'Enable crisis keyword detection',
        },
        {
            key: 'email_notifications_enabled',
            value: 'true',
            description: 'Enable email notifications',
        },
        {
            key: 'maintenance_mode',
            value: 'false',
            description: 'Enable maintenance mode',
        },
    ];
    for (const setting of systemSettings) {
        await prisma.systemSetting.upsert({
            where: { key: setting.key },
            update: {},
            create: setting,
        });
    }
    console.log('✅ System settings created');
    const forumPosts = [
        {
            authorId: patient.id,
            title: 'Dealing with anxiety during job interviews',
            content: 'I have been struggling with severe anxiety whenever I have job interviews. My heart races, I sweat, and I can barely speak. Has anyone else experienced this? What techniques have helped you?',
            category: 'Anxiety',
            tags: JSON.stringify(['anxiety', 'job-interviews', 'career', 'stress']),
            isAnonymous: false,
        },
        {
            authorId: patient.id,
            title: 'Anonymous: Feeling overwhelmed',
            content: 'I feel like everything is falling apart. Work is stressful, my relationship is struggling, and I can barely get out of bed some days. I know I should seek help but I am scared.',
            category: 'General',
            tags: JSON.stringify(['depression', 'overwhelmed', 'relationships', 'work-stress']),
            isAnonymous: true,
        },
    ];
    for (const post of forumPosts) {
        await prisma.forumPost.create({
            data: post,
        });
    }
    console.log('✅ Sample forum posts created');
    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Test Accounts Created:');
    console.log(`👑 Admin: ${admin.email} / ${process.env.ADMIN_PASSWORD || 'Admin123!@#'}`);
    console.log(`🛡️  Moderator: ${moderator.email} / Moderator123!@#`);
    console.log(`👨‍⚕️ Psychiatrist: ${psychiatrist.email} / Psychiatrist123!@#`);
    console.log(`👤 Patient: ${patient.email} / Patient123!@#`);
}
main()
    .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map