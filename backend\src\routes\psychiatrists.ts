import { Router } from 'express';
import { z } from 'zod';
import { UserRole } from '@prisma/client';
import { prisma } from '../../server';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { requireRole, requireEmailVerification, optionalAuth } from '../middleware/auth';

const router = Router();

// Validation schemas
const updateProfileSchema = z.object({
  specialties: z.array(z.string()).optional(),
  bio: z.string().max(2000).optional(),
  education: z.array(z.string()).optional(),
  experience: z.string().max(1000).optional(),
  languages: z.array(z.string()).optional(),
  hourlyRate: z.number().min(0).max(1000).optional(),
  acceptsInsurance: z.boolean().optional(),
});

const availabilitySchema = z.object({
  availability: z.array(z.object({
    dayOfWeek: z.number().min(0).max(6),
    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    isAvailable: z.boolean(),
  })),
});

// Get all verified psychiatrists (public)
router.get('/', asyncHandler(async (req, res) => {
  const query = z.object({
    page: z.union([z.string(), z.number()]).transform(val => Number(val)).optional(),
    limit: z.union([z.string(), z.number()]).transform(val => Number(val)).optional(),
    specialty: z.string().optional(),
    minRate: z.union([z.string(), z.number()]).transform(val => Number(val)).optional(),
    maxRate: z.union([z.string(), z.number()]).transform(val => Number(val)).optional(),
    acceptsInsurance: z.union([z.string(), z.boolean()]).transform(val => val === 'true' || val === true).optional(),
    search: z.string().optional(),
  }).parse(req.query);

  const page = query.page || 1;
  const limit = query.limit || 20;

  const where: any = {
    role: UserRole.PSYCHIATRIST,
    status: 'ACTIVE',
    psychiatristProfile: {
      verified: true,
    },
  };

  // Build search conditions
  const profileWhere: any = { verified: true };

  if (query.specialty) {
    profileWhere.specialties = {
      contains: query.specialty,
    };
  }

  if (query.minRate !== undefined || query.maxRate !== undefined) {
    profileWhere.hourlyRate = {};
    if (query.minRate !== undefined) {
      profileWhere.hourlyRate.gte = query.minRate;
    }
    if (query.maxRate !== undefined) {
      profileWhere.hourlyRate.lte = query.maxRate;
    }
  }

  if (query.acceptsInsurance !== undefined) {
    profileWhere.acceptsInsurance = query.acceptsInsurance;
  }

  if (query.search) {
    where.OR = [
      { firstName: { contains: query.search } },
      { lastName: { contains: query.search } },
      { psychiatristProfile: { bio: { contains: query.search } } },
    ];
  }

  where.psychiatristProfile = profileWhere;

  const [psychiatrists, total] = await Promise.all([
    prisma.user.findMany({
      where,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        profileImage: true,
        psychiatristProfile: {
          select: {
            specialties: true,
            bio: true,
            education: true,
            experience: true,
            languages: true,
            hourlyRate: true,
            acceptsInsurance: true,
            verified: true,
          },
        },
        _count: {
          select: {
            psychiatristSessions: {
              where: { status: 'COMPLETED' },
            },
            receivedReviews: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    }),
    prisma.user.count({ where }),
  ]);

  // Parse JSON fields and calculate average rating
  const psychiatristsWithRatings = await Promise.all(
    psychiatrists.map(async (psychiatrist) => {
      const avgRating = await prisma.review.aggregate({
        where: { targetId: psychiatrist.id },
        _avg: { rating: true },
      });

      return {
        ...psychiatrist,
        psychiatristProfile: psychiatrist.psychiatristProfile ? {
          ...psychiatrist.psychiatristProfile,
          specialties: psychiatrist.psychiatristProfile.specialties 
            ? JSON.parse(psychiatrist.psychiatristProfile.specialties) 
            : [],
          education: psychiatrist.psychiatristProfile.education 
            ? JSON.parse(psychiatrist.psychiatristProfile.education) 
            : [],
          languages: psychiatrist.psychiatristProfile.languages 
            ? JSON.parse(psychiatrist.psychiatristProfile.languages) 
            : [],
        } : null,
        stats: {
          completedSessions: psychiatrist._count.psychiatristSessions,
          totalReviews: psychiatrist._count.receivedReviews,
          averageRating: avgRating._avg.rating || 0,
        },
      };
    })
  );

  res.json({
    success: true,
    data: {
      psychiatrists: psychiatristsWithRatings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    },
  });
}));

// Get specific psychiatrist details
router.get('/:psychiatristId', asyncHandler(async (req, res) => {
  const { psychiatristId } = req.params;

  const psychiatrist = await prisma.user.findUnique({
    where: { 
      id: psychiatristId,
      role: UserRole.PSYCHIATRIST,
      status: 'ACTIVE',
    },
    include: {
      psychiatristProfile: {
        include: {
          availability: true,
        },
      },
      receivedReviews: {
        where: { isApproved: true },
        include: {
          author: {
            select: {
              firstName: true,
              lastName: true,
              profileImage: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      _count: {
        select: {
          psychiatristSessions: {
            where: { status: 'COMPLETED' },
          },
          receivedReviews: {
            where: { isApproved: true },
          },
        },
      },
    },
  });

  if (!psychiatrist || !psychiatrist.psychiatristProfile?.verified) {
    throw new CustomError('Psychiatrist not found', 404);
  }

  // Calculate average rating
  const avgRating = await prisma.review.aggregate({
    where: { targetId: psychiatristId, isApproved: true },
    _avg: { rating: true },
  });

  // Parse JSON fields
  const psychiatristData = {
    ...psychiatrist,
    psychiatristProfile: {
      ...psychiatrist.psychiatristProfile,
      specialties: psychiatrist.psychiatristProfile.specialties 
        ? JSON.parse(psychiatrist.psychiatristProfile.specialties) 
        : [],
      education: psychiatrist.psychiatristProfile.education 
        ? JSON.parse(psychiatrist.psychiatristProfile.education) 
        : [],
      languages: psychiatrist.psychiatristProfile.languages 
        ? JSON.parse(psychiatrist.psychiatristProfile.languages) 
        : [],
    },
    stats: {
      completedSessions: psychiatrist._count.psychiatristSessions,
      totalReviews: psychiatrist._count.receivedReviews,
      averageRating: avgRating._avg.rating || 0,
    },
    reviews: psychiatrist.receivedReviews.map(review => ({
      ...review,
      author: review.isAnonymous ? null : review.author,
    })),
  };

  res.json({
    success: true,
    data: psychiatristData,
  });
}));

// Update psychiatrist profile (psychiatrists only)
router.put('/profile', requireEmailVerification, requireRole([UserRole.PSYCHIATRIST]), asyncHandler(async (req, res) => {
  const validatedData = updateProfileSchema.parse(req.body);
  const userId = req.user!.id;

  const updateData: any = {};

  if (validatedData.specialties) {
    updateData.specialties = JSON.stringify(validatedData.specialties);
  }
  if (validatedData.bio) updateData.bio = validatedData.bio;
  if (validatedData.education) {
    updateData.education = JSON.stringify(validatedData.education);
  }
  if (validatedData.experience) updateData.experience = validatedData.experience;
  if (validatedData.languages) {
    updateData.languages = JSON.stringify(validatedData.languages);
  }
  if (validatedData.hourlyRate !== undefined) updateData.hourlyRate = validatedData.hourlyRate;
  if (validatedData.acceptsInsurance !== undefined) updateData.acceptsInsurance = validatedData.acceptsInsurance;

  const profile = await prisma.psychiatristProfile.update({
    where: { userId },
    data: updateData,
  });

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: {
      ...profile,
      specialties: profile.specialties ? JSON.parse(profile.specialties) : [],
      education: profile.education ? JSON.parse(profile.education) : [],
      languages: profile.languages ? JSON.parse(profile.languages) : [],
    },
  });
}));

// Update availability (psychiatrists only)
router.put('/availability', requireEmailVerification, requireRole([UserRole.PSYCHIATRIST]), asyncHandler(async (req, res) => {
  const { availability } = availabilitySchema.parse(req.body);
  const userId = req.user!.id;

  // Get psychiatrist profile
  const profile = await prisma.psychiatristProfile.findUnique({
    where: { userId },
  });

  if (!profile) {
    throw new CustomError('Psychiatrist profile not found', 404);
  }

  // Delete existing availability
  await prisma.availability.deleteMany({
    where: { psychiatristId: profile.id },
  });

  // Create new availability slots
  const availabilityData = availability.map(slot => ({
    psychiatristId: profile.id,
    ...slot,
  }));

  await prisma.availability.createMany({
    data: availabilityData,
  });

  res.json({
    success: true,
    message: 'Availability updated successfully',
  });
}));

// Get psychiatrist's availability
router.get('/:psychiatristId/availability', asyncHandler(async (req, res) => {
  const { psychiatristId } = req.params;

  const profile = await prisma.psychiatristProfile.findUnique({
    where: { userId: psychiatristId },
    include: { availability: true },
  });

  if (!profile) {
    throw new CustomError('Psychiatrist not found', 404);
  }

  res.json({
    success: true,
    data: profile.availability,
  });
}));

export default router;
