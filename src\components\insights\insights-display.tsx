import React from 'react';
import type { JournalInsightsOutput } from '@/ai/flows/journal-insights';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, AlertTriangle, Smile } from 'lucide-react';

interface InsightsDisplayProps {
  insights: JournalInsightsOutput;
}

export function InsightsDisplay({ insights }: InsightsDisplayProps) {
  return (
    <div className="space-y-6">
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center text-xl">
            <TrendingUp className="mr-2 h-6 w-6 text-primary" />
            Mood Patterns
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-foreground/90">{insights.moodPatterns}</p>
        </CardContent>
      </Card>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center text-xl">
            <AlertTriangle className="mr-2 h-6 w-6 text-destructive" />
            Potential Triggers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-foreground/90">{insights.potentialTriggers}</p>
        </CardContent>
      </Card>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center text-xl">
            <Smile className="mr-2 h-6 w-6 text-secondary" />
            Overall Sentiment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-foreground/90 capitalize">{insights.overallSentiment}</p>
        </CardContent>
      </Card>
    </div>
  );
}
