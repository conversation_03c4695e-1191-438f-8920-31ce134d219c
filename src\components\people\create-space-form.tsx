
'use client';

import React, { useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useToast } from '@/hooks/use-toast';
import { PlusCircle, UploadCloud, Tag, Users } from 'lucide-react';
import { DialogClose } from '@/components/ui/dialog';


const spaceFormSchema = z.object({
  name: z.string().min(3, "Space name must be at least 3 characters.").max(100),
  description: z.string().min(10, "Description must be at least 10 characters.").max(500),
  tags: z.string().refine(value => value.split(',').every(tag => tag.trim().length > 0), {
    message: "Tags cannot be empty and should be comma-separated.",
  }).optional(),
  spaceType: z.enum(['free', 'paid']),
  monthlyPrice: z.coerce.number().min(0).optional(),
  bannerImage: z.any().optional(), // Placeholder for file upload
  profileImage: z.any().optional(), // Placeholder for file upload
}).refine(data => {
    if (data.spaceType === 'paid') {
        return data.monthlyPrice !== undefined && data.monthlyPrice > 0;
    }
    return true;
}, {
    message: "Monthly price must be greater than 0 for paid spaces.",
    path: ["monthlyPrice"],
});

type SpaceFormValues = z.infer<typeof spaceFormSchema>;

interface CreateSpaceFormProps {
  onSpaceCreate?: (data: SpaceFormValues) => void; // Optional callback
}

export function CreateSpaceForm({ onSpaceCreate }: CreateSpaceFormProps) {
  const { toast } = useToast();
  const [spaceType, setSpaceType] = useState<'free' | 'paid'>('free');

  const { register, handleSubmit, control, setValue, formState: { errors, isSubmitting } } = useForm<SpaceFormValues>({
    resolver: zodResolver(spaceFormSchema),
    defaultValues: {
      name: '',
      description: '',
      tags: '',
      spaceType: 'free',
      monthlyPrice: 0,
    },
  });

  const onSubmit: SubmitHandler<SpaceFormValues> = async (data) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log("Space creation data:", data);
    toast({
      title: "Space Created (Simulated)",
      description: `The space "${data.name}" has been successfully created.`,
    });
    if (onSpaceCreate) {
      onSpaceCreate(data);
    }
    // Reset form or close dialog would happen here in a real app
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <Label htmlFor="name">Space Name</Label>
        <Input id="name" {...register('name')} placeholder="e.g., Mindful Living Community" />
        {errors.name && <p className="text-sm text-destructive mt-1">{errors.name.message}</p>}
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea id="description" {...register('description')} placeholder="Tell us about your space..." rows={4} />
        {errors.description && <p className="text-sm text-destructive mt-1">{errors.description.message}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="bannerImage">Banner Image (Optional)</Label>
          <div className="flex items-center p-2 border border-dashed rounded-md">
            <UploadCloud className="h-8 w-8 text-muted-foreground mr-3" />
            <Input id="bannerImage" type="file" {...register('bannerImage')} className="text-sm file:mr-2 file:py-1 file:px-2 file:rounded-full file:border-0 file:text-xs file:bg-muted file:text-muted-foreground hover:file:bg-muted/80" />
          </div>
          {errors.bannerImage && <p className="text-sm text-destructive mt-1">{typeof errors.bannerImage.message === 'string' ? errors.bannerImage.message : 'Invalid file'}</p>}
        </div>
        <div>
          <Label htmlFor="profileImage">Profile Image (Optional)</Label>
           <div className="flex items-center p-2 border border-dashed rounded-md">
            <Users className="h-8 w-8 text-muted-foreground mr-3" />
            <Input id="profileImage" type="file" {...register('profileImage')} className="text-sm file:mr-2 file:py-1 file:px-2 file:rounded-full file:border-0 file:text-xs file:bg-muted file:text-muted-foreground hover:file:bg-muted/80" />
          </div>
          {errors.profileImage && <p className="text-sm text-destructive mt-1">{typeof errors.profileImage.message === 'string' ? errors.profileImage.message : 'Invalid file'}</p>}
        </div>
      </div>
      
      <div>
        <Label htmlFor="tags">Tags (comma-separated)</Label>
        <div className="flex items-center border rounded-md px-2">
          <Tag className="h-4 w-4 text-muted-foreground mr-2" />
          <Input id="tags" {...register('tags')} placeholder="e.g., mindfulness, anxiety, support" className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 pl-0" />
        </div>
        {errors.tags && <p className="text-sm text-destructive mt-1">{errors.tags.message}</p>}
      </div>

      <div>
        <Label>Space Type</Label>
        <Controller
            name="spaceType"
            control={control}
            render={({ field }) => (
                <RadioGroup
                    onValueChange={(value) => {
                        field.onChange(value);
                        setSpaceType(value as 'free' | 'paid');
                        if (value === 'free') {
                            setValue('monthlyPrice', 0);
                        }
                    }}
                    value={field.value}
                    className="flex gap-4 mt-1"
                >
                    <Label htmlFor="free-type" className="flex items-center gap-2 p-3 border rounded-md cursor-pointer hover:bg-muted/50 has-[:checked]:bg-primary/10 has-[:checked]:border-primary">
                        <RadioGroupItem value="free" id="free-type" />
                        Free
                    </Label>
                    <Label htmlFor="paid-type" className="flex items-center gap-2 p-3 border rounded-md cursor-pointer hover:bg-muted/50 has-[:checked]:bg-primary/10 has-[:checked]:border-primary">
                        <RadioGroupItem value="paid" id="paid-type" />
                        Subscription-based
                    </Label>
                </RadioGroup>
            )}
        />
        {errors.spaceType && <p className="text-sm text-destructive mt-1">{errors.spaceType.message}</p>}
      </div>

      {spaceType === 'paid' && (
        <div>
          <Label htmlFor="monthlyPrice">Monthly Subscription Price (Birr)</Label>
          <Input id="monthlyPrice" type="number" {...register('monthlyPrice')} placeholder="e.g., 500" />
          {errors.monthlyPrice && <p className="text-sm text-destructive mt-1">{errors.monthlyPrice.message}</p>}
        </div>
      )}
        <div className="flex justify-end gap-2 pt-4">
            <DialogClose asChild>
                 <Button type="button" variant="outline">Cancel</Button>
            </DialogClose>
            <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Creating...' : <><PlusCircle className="mr-2 h-4 w-4" /> Create Space</>}
            </Button>
        </div>
    </form>
  );
}
