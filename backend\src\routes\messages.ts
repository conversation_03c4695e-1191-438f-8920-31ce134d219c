import { Router } from 'express';
import { z } from 'zod';
import { UserRole, MessageType } from '@prisma/client';
import { prisma } from '../../server';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { requireEmailVerification } from '../middleware/auth';

const router = Router();

// Validation schemas
const sendMessageSchema = z.object({
  receiverId: z.string().cuid().optional(),
  sessionId: z.string().cuid().optional(),
  content: z.string().min(1, 'Message content is required').max(5000, 'Message too long'),
  messageType: z.enum(['TEXT', 'IMAGE', 'FILE', 'SYSTEM']).optional().default('TEXT'),
  fileUrl: z.string().url().optional(),
  fileName: z.string().optional(),
});

const messageQuerySchema = z.object({
  page: z.string().transform(Number).optional().default(1),
  limit: z.string().transform(Number).optional().default(50),
  receiverId: z.string().cuid().optional(),
  sessionId: z.string().cuid().optional(),
  before: z.string().datetime().optional(),
  after: z.string().datetime().optional(),
});

// Send a message
router.post('/', requireEmailVerification, asyncHandler(async (req, res) => {
  const validatedData = sendMessageSchema.parse(req.body);
  const senderId = req.user!.id;

  // Validate that either receiverId or sessionId is provided
  if (!validatedData.receiverId && !validatedData.sessionId) {
    throw new CustomError('Either receiverId or sessionId must be provided', 400);
  }

  // If receiverId is provided, validate the receiver exists and user can message them
  if (validatedData.receiverId) {
    const receiver = await prisma.user.findUnique({
      where: { id: validatedData.receiverId },
      select: { id: true, role: true, status: true },
    });

    if (!receiver) {
      throw new CustomError('Receiver not found', 404);
    }

    if (receiver.status !== 'ACTIVE') {
      throw new CustomError('Cannot send message to inactive user', 400);
    }

    // Check if users have a relationship (patient-psychiatrist)
    const hasRelationship = await prisma.session.findFirst({
      where: {
        OR: [
          { patientId: senderId, psychiatristId: validatedData.receiverId },
          { patientId: validatedData.receiverId, psychiatristId: senderId },
        ],
      },
    });

    if (!hasRelationship && req.user!.role !== UserRole.ADMIN && req.user!.role !== UserRole.MODERATOR) {
      throw new CustomError('Cannot send message to this user', 403);
    }
  }

  // If sessionId is provided, validate the session and user's participation
  if (validatedData.sessionId) {
    const session = await prisma.session.findUnique({
      where: { id: validatedData.sessionId },
      select: { id: true, patientId: true, psychiatristId: true, status: true },
    });

    if (!session) {
      throw new CustomError('Session not found', 404);
    }

    if (session.patientId !== senderId && session.psychiatristId !== senderId) {
      throw new CustomError('You are not a participant in this session', 403);
    }
  }

  // Create the message
  const message = await prisma.message.create({
    data: {
      senderId,
      receiverId: validatedData.receiverId,
      sessionId: validatedData.sessionId,
      content: validatedData.content,
      messageType: validatedData.messageType,
      fileUrl: validatedData.fileUrl,
      fileName: validatedData.fileName,
    },
    include: {
      sender: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          profileImage: true,
        },
      },
      receiver: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },
      },
    },
  });

  // TODO: Implement real-time message delivery via Socket.IO
  // TODO: Implement AI content moderation
  // TODO: Check for crisis keywords

  res.status(201).json({
    success: true,
    message: 'Message sent successfully',
    data: message,
  });
}));

// Get messages (conversations or session messages)
router.get('/', requireEmailVerification, asyncHandler(async (req, res) => {
  const query = messageQuerySchema.parse(req.query);
  const userId = req.user!.id;

  const where: any = {};

  // Filter by conversation or session
  if (query.receiverId) {
    where.OR = [
      { senderId: userId, receiverId: query.receiverId },
      { senderId: query.receiverId, receiverId: userId },
    ];
  } else if (query.sessionId) {
    // Verify user is part of the session
    const session = await prisma.session.findUnique({
      where: { id: query.sessionId },
      select: { patientId: true, psychiatristId: true },
    });

    if (!session) {
      throw new CustomError('Session not found', 404);
    }

    if (session.patientId !== userId && session.psychiatristId !== userId) {
      throw new CustomError('Access denied', 403);
    }

    where.sessionId = query.sessionId;
  } else {
    // Get all messages for the user
    where.OR = [
      { senderId: userId },
      { receiverId: userId },
    ];
  }

  // Date filters
  if (query.before || query.after) {
    where.createdAt = {};
    if (query.before) {
      where.createdAt.lt = new Date(query.before);
    }
    if (query.after) {
      where.createdAt.gt = new Date(query.after);
    }
  }

  const [messages, total] = await Promise.all([
    prisma.message.findMany({
      where,
      include: {
        sender: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profileImage: true,
          },
        },
        receiver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    }),
    prisma.message.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      messages,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
        hasNext: query.page * query.limit < total,
        hasPrev: query.page > 1,
      },
    },
  });
}));

// Get conversations list
router.get('/conversations', requireEmailVerification, asyncHandler(async (req, res) => {
  const userId = req.user!.id;

  // Get unique conversations with latest message
  const conversations = await prisma.$queryRaw`
    SELECT 
      CASE 
        WHEN m.senderId = ${userId} THEN m.receiverId 
        ELSE m.senderId 
      END as otherUserId,
      u.firstName,
      u.lastName,
      u.profileImage,
      u.role,
      m.content as lastMessage,
      m.messageType as lastMessageType,
      m.createdAt as lastMessageAt,
      m.isRead,
      COUNT(CASE WHEN m.receiverId = ${userId} AND m.isRead = false THEN 1 END) as unreadCount
    FROM messages m
    INNER JOIN users u ON (
      CASE 
        WHEN m.senderId = ${userId} THEN u.id = m.receiverId 
        ELSE u.id = m.senderId 
      END
    )
    WHERE (m.senderId = ${userId} OR m.receiverId = ${userId})
      AND m.sessionId IS NULL
    GROUP BY otherUserId, u.firstName, u.lastName, u.profileImage, u.role, m.content, m.messageType, m.createdAt, m.isRead
    HAVING m.createdAt = (
      SELECT MAX(m2.createdAt) 
      FROM messages m2 
      WHERE (
        (m2.senderId = ${userId} AND m2.receiverId = otherUserId) OR 
        (m2.senderId = otherUserId AND m2.receiverId = ${userId})
      ) AND m2.sessionId IS NULL
    )
    ORDER BY m.createdAt DESC
  `;

  res.json({
    success: true,
    data: conversations,
  });
}));

// Mark message as read
router.put('/:messageId/read', requireEmailVerification, asyncHandler(async (req, res) => {
  const { messageId } = req.params;
  const userId = req.user!.id;

  const message = await prisma.message.findUnique({
    where: { id: messageId },
    select: { id: true, receiverId: true, isRead: true },
  });

  if (!message) {
    throw new CustomError('Message not found', 404);
  }

  if (message.receiverId !== userId) {
    throw new CustomError('Can only mark your own received messages as read', 403);
  }

  if (message.isRead) {
    return res.json({
      success: true,
      message: 'Message already marked as read',
    });
  }

  await prisma.message.update({
    where: { id: messageId },
    data: { isRead: true, readAt: new Date() },
  });

  res.json({
    success: true,
    message: 'Message marked as read',
  });
}));

// Mark all messages in a conversation as read
router.put('/conversations/:otherUserId/read', requireEmailVerification, asyncHandler(async (req, res) => {
  const { otherUserId } = req.params;
  const userId = req.user!.id;

  // Verify the other user exists
  const otherUser = await prisma.user.findUnique({
    where: { id: otherUserId },
    select: { id: true },
  });

  if (!otherUser) {
    throw new CustomError('User not found', 404);
  }

  await prisma.message.updateMany({
    where: {
      senderId: otherUserId,
      receiverId: userId,
      isRead: false,
      sessionId: null,
    },
    data: { isRead: true, readAt: new Date() },
  });

  res.json({
    success: true,
    message: 'All messages marked as read',
  });
}));

// Delete a message (soft delete)
router.delete('/:messageId', requireEmailVerification, asyncHandler(async (req, res) => {
  const { messageId } = req.params;
  const userId = req.user!.id;

  const message = await prisma.message.findUnique({
    where: { id: messageId },
    select: { id: true, senderId: true, content: true },
  });

  if (!message) {
    throw new CustomError('Message not found', 404);
  }

  if (message.senderId !== userId) {
    throw new CustomError('Can only delete your own messages', 403);
  }

  // Soft delete by updating content
  await prisma.message.update({
    where: { id: messageId },
    data: { 
      content: '[Message deleted]',
      isEdited: true,
      editedAt: new Date(),
    },
  });

  res.json({
    success: true,
    message: 'Message deleted successfully',
  });
}));

export default router;
