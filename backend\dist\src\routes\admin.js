"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
const server_1 = require("../../server");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.use((0, auth_1.requireRole)([client_1.UserRole.ADMIN]));
router.get('/stats', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const [totalUsers, totalSessions, totalRevenue, activeUsers, pendingApprovals, flaggedContent] = await Promise.all([
        server_1.prisma.user.count(),
        server_1.prisma.session.count(),
        server_1.prisma.payment.aggregate({
            where: { status: 'COMPLETED' },
            _sum: { amount: true },
        }).then(result => result._sum.amount || 0),
        server_1.prisma.user.count({ where: { status: 'ACTIVE' } }),
        server_1.prisma.user.count({
            where: {
                role: client_1.UserRole.PSYCHIATRIST,
                status: 'PENDING_APPROVAL'
            }
        }),
        server_1.prisma.forumPost.count({ where: { isFlagged: true } }),
    ]);
    res.json({
        success: true,
        data: {
            totalUsers,
            totalSessions,
            totalRevenue,
            activeUsers,
            pendingApprovals,
            flaggedContent,
        },
    });
}));
router.get('/pending-approvals', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const pendingPsychiatrists = await server_1.prisma.user.findMany({
        where: {
            role: client_1.UserRole.PSYCHIATRIST,
            status: 'PENDING_APPROVAL',
        },
        include: {
            psychiatristProfile: true,
        },
        orderBy: { createdAt: 'asc' },
    });
    res.json({
        success: true,
        data: pendingPsychiatrists,
    });
}));
router.put('/approve-psychiatrist/:userId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { userId } = req.params;
    const { approved, reason } = zod_1.z.object({
        approved: zod_1.z.boolean(),
        reason: zod_1.z.string().optional(),
    }).parse(req.body);
    const user = await server_1.prisma.user.findUnique({
        where: { id: userId },
        include: { psychiatristProfile: true },
    });
    if (!user || user.role !== client_1.UserRole.PSYCHIATRIST) {
        throw new errorHandler_1.CustomError('Psychiatrist not found', 404);
    }
    if (user.status !== 'PENDING_APPROVAL') {
        throw new errorHandler_1.CustomError('User is not pending approval', 400);
    }
    if (approved) {
        await server_1.prisma.$transaction([
            server_1.prisma.user.update({
                where: { id: userId },
                data: { status: client_1.UserStatus.ACTIVE },
            }),
            server_1.prisma.psychiatristProfile.update({
                where: { userId },
                data: { verified: true },
            }),
        ]);
    }
    else {
        await server_1.prisma.user.update({
            where: { id: userId },
            data: { status: client_1.UserStatus.SUSPENDED },
        });
    }
    res.json({
        success: true,
        message: `Psychiatrist ${approved ? 'approved' : 'rejected'} successfully`,
    });
}));
router.get('/users', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const query = zod_1.z.object({
        page: zod_1.z.string().transform(Number).optional().default(1),
        limit: zod_1.z.string().transform(Number).optional().default(20),
        role: zod_1.z.nativeEnum(client_1.UserRole).optional(),
        status: zod_1.z.nativeEnum(client_1.UserStatus).optional(),
        search: zod_1.z.string().optional(),
    }).parse(req.query);
    const where = {};
    if (query.role)
        where.role = query.role;
    if (query.status)
        where.status = query.status;
    if (query.search) {
        where.OR = [
            { email: { contains: query.search } },
            { firstName: { contains: query.search } },
            { lastName: { contains: query.search } },
        ];
    }
    const [users, total] = await Promise.all([
        server_1.prisma.user.findMany({
            where,
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                status: true,
                emailVerified: true,
                createdAt: true,
                lastLoginAt: true,
            },
            orderBy: { createdAt: 'desc' },
            skip: (query.page - 1) * query.limit,
            take: query.limit,
        }),
        server_1.prisma.user.count({ where }),
    ]);
    res.json({
        success: true,
        data: {
            users,
            pagination: {
                page: query.page,
                limit: query.limit,
                total,
                totalPages: Math.ceil(total / query.limit),
                hasNext: query.page * query.limit < total,
                hasPrev: query.page > 1,
            },
        },
    });
}));
router.put('/users/:userId/status', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { userId } = req.params;
    const { status, reason } = zod_1.z.object({
        status: zod_1.z.nativeEnum(client_1.UserStatus),
        reason: zod_1.z.string().optional(),
    }).parse(req.body);
    const user = await server_1.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true, status: true },
    });
    if (!user) {
        throw new errorHandler_1.CustomError('User not found', 404);
    }
    if (user.role === client_1.UserRole.ADMIN && status === client_1.UserStatus.SUSPENDED) {
        throw new errorHandler_1.CustomError('Cannot suspend admin users', 403);
    }
    await server_1.prisma.user.update({
        where: { id: userId },
        data: { status },
    });
    await server_1.prisma.moderationAction.create({
        data: {
            moderatorId: req.user.id,
            targetUserId: userId,
            action: `status_change_${status}`,
            reason: reason || `Status changed to ${status}`,
        },
    });
    res.json({
        success: true,
        message: 'User status updated successfully',
    });
}));
router.get('/settings', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const settings = await server_1.prisma.systemSetting.findMany({
        orderBy: { key: 'asc' },
    });
    res.json({
        success: true,
        data: settings,
    });
}));
router.put('/settings/:key', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { key } = req.params;
    const { value } = zod_1.z.object({
        value: zod_1.z.string(),
    }).parse(req.body);
    const setting = await server_1.prisma.systemSetting.upsert({
        where: { key },
        update: { value },
        create: { key, value },
    });
    res.json({
        success: true,
        message: 'Setting updated successfully',
        data: setting,
    });
}));
exports.default = router;
//# sourceMappingURL=admin.js.map