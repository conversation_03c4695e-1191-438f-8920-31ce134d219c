
import Link from 'next/link';
import { Logo } from '@/components/icons/logo';
import { APP_NAME } from '@/lib/constants';

export function LandingFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t border-border/40 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center mb-4 md:mb-0">
            <Logo className="h-8 w-8 mr-2 text-primary" />
            <span className="text-lg font-semibold text-foreground">{APP_NAME}</span>
          </div>
          <nav className="flex flex-wrap justify-center space-x-4 md:space-x-6 mb-4 md:mb-0">
            {/* Placeholder links */}
            <Link href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Privacy Policy
            </Link>
            <Link href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Terms of Service
            </Link>
            <Link href="#features" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Features
            </Link>
             <Link href="#how-it-works" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              How It Works
            </Link>
          </nav>
          <p className="text-sm text-muted-foreground">
            &copy; {currentYear} {APP_NAME}. All rights reserved.
          </p>
        </div>
         <p className="text-center text-xs text-muted-foreground mt-8">
          WELL is a tool for personal reflection and well-being. It is not a substitute for professional medical advice, diagnosis, or treatment.
        </p>
      </div>
    </footer>
  );
}

