
'use client';

import React from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Gem, Star, Users, ShieldCheck, CreditCard } from 'lucide-react';
import type { SpaceSubscriptionTier, Space } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

interface SubscriptionTiersSectionProps {
  space: Space;
}

const tierIcons = {
    Free: <Star className="h-5 w-5 text-yellow-500" />,
    Supporter: <Gem className="h-5 w-5 text-blue-500" />,
    Premium: <ShieldCheck className="h-5 w-5 text-purple-500" />,
}

export function SubscriptionTiersSection({ space }: SubscriptionTiersSectionProps) {
  const { toast } = useToast();

  const handleSubscribe = (tierName: string, price: number) => {
    toast({
        title: "Subscription (Simulated)",
        description: `You've chosen to subscribe to the "${tierName}" tier for Birr ${price}/month.`,
    });
    // Actual subscription logic would go here
  };

  if (!space.isSubscriptionBased && (!space.subscriptionTiers || space.subscriptionTiers.length === 0)) {
    return (
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <Users className="mr-2 h-6 w-6 text-primary" />
            Join this Space
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">This space is free to join for everyone!</p>
          <Button className="w-full" onClick={() => toast({title: "Joined Space!", description: `You are now following ${space.name}.`})}>
            Join Space for Free
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  // Fallback if isSubscriptionBased is true but no tiers are defined.
  const tiersToDisplay = space.subscriptionTiers && space.subscriptionTiers.length > 0 
    ? space.subscriptionTiers 
    : [{ id: 'default-paid', name: 'Premium Access' as 'Premium', pricePerMonth: space.isSubscriptionBased ? 500 : 0, features: ['Full access to content', 'Live event participation', 'Community discussions'] }];


  return (
    <Card className="shadow-md">
      <CardHeader>
        <CardTitle className="flex items-center text-2xl">
          <CreditCard className="mr-2 h-6 w-6 text-primary" />
          Subscription Tiers
        </CardTitle>
        <CardDescription>
          Support the host and unlock exclusive content by subscribing to a tier.
        </CardDescription>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tiersToDisplay.map(tier => (
          <Card key={tier.id} className={`flex flex-col ${tier.name === 'Premium' ? 'border-primary shadow-lg' : ''}`}>
            <CardHeader className="items-center text-center">
              <div className="mb-2">{tierIcons[tier.name] || <Star className="h-5 w-5" />}</div>
              <CardTitle className="text-xl">{tier.name}</CardTitle>
              {tier.pricePerMonth > 0 ? (
                <p className="text-2xl font-bold text-primary">
                  Birr {tier.pricePerMonth}
                  <span className="text-sm font-normal text-muted-foreground">/month</span>
                </p>
              ) : (
                <p className="text-2xl font-bold text-primary">Free</p>
              )}
            </CardHeader>
            <CardContent className="flex-grow">
              <ul className="space-y-2 text-sm text-muted-foreground">
                {tier.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button 
                className="w-full" 
                variant={tier.name === 'Premium' ? 'default' : 'outline'}
                onClick={() => handleSubscribe(tier.name, tier.pricePerMonth)}
                disabled={tier.pricePerMonth === 0 && tier.name === 'Free'} // Disable if already free
              >
                {tier.pricePerMonth > 0 ? `Subscribe to ${tier.name}` : 'Current Tier (Free)'}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </CardContent>
      <CardFooter>
        <p className="text-xs text-muted-foreground">
            Subscription tiers can grant access to private live events, premium resources, and more.
        </p>
      </CardFooter>
    </Card>
  );
}
