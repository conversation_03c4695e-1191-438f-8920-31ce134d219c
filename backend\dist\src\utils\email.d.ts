import { EmailTemplate } from '../types';
export declare class EmailService {
    private transporter;
    constructor();
    sendEmail(template: EmailTemplate): Promise<void>;
    generateEmailVerificationTemplate(email: string, token: string, firstName: string): EmailTemplate;
    generatePasswordResetTemplate(email: string, token: string, firstName: string): EmailTemplate;
    generateSessionReminderTemplate(email: string, firstName: string, sessionDate: Date, psychiatristName: string): EmailTemplate;
    sendEmailVerification(email: string, token: string, firstName: string): Promise<void>;
    sendPasswordReset(email: string, token: string, firstName: string): Promise<void>;
    sendSessionReminder(email: string, firstName: string, sessionDate: Date, psychiatristName: string): Promise<void>;
}
export declare const emailService: EmailService;
//# sourceMappingURL=email.d.ts.map