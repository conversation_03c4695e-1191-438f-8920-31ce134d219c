import React from 'react';
import type { PersonalizedRecommendationsOutput } from '@/ai/flows/personalized-recommendations';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Sparkles } from 'lucide-react';

interface RecommendationsDisplayProps {
  recommendations: PersonalizedRecommendationsOutput;
}

export function RecommendationsDisplay({ recommendations }: RecommendationsDisplayProps) {
  // Assuming recommendations.recommendations is a string with markdown-like newlines
  const formattedRecommendations = recommendations.recommendations.split('\n').map((line, index) => (
    <span key={index}>
      {line}
      <br />
    </span>
  ));

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center text-xl">
          <Sparkles className="mr-2 h-6 w-6 text-primary" />
          Your Personalized Recommendations
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-foreground/90 space-y-2">
          {formattedRecommendations}
        </div>
      </CardContent>
    </Card>
  );
}
