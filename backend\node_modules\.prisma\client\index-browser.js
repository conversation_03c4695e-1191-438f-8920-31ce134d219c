
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  role: 'role',
  status: 'status',
  emailVerified: 'emailVerified',
  emailVerificationToken: 'emailVerificationToken',
  passwordResetToken: 'passwordResetToken',
  passwordResetExpires: 'passwordResetExpires',
  twoFactorEnabled: 'twoFactorEnabled',
  twoFactorSecret: 'twoFactorSecret',
  profileImage: 'profileImage',
  phoneNumber: 'phoneNumber',
  dateOfBirth: 'dateOfBirth',
  timezone: 'timezone',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLoginAt: 'lastLoginAt'
};

exports.Prisma.PatientProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  emergencyContact: 'emergencyContact',
  medicalHistory: 'medicalHistory',
  currentMedications: 'currentMedications',
  allergies: 'allergies',
  preferredLanguages: 'preferredLanguages',
  insuranceInfo: 'insuranceInfo',
  consentToAI: 'consentToAI',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PsychiatristProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  licenseNumber: 'licenseNumber',
  specialties: 'specialties',
  bio: 'bio',
  education: 'education',
  experience: 'experience',
  languages: 'languages',
  hourlyRate: 'hourlyRate',
  acceptsInsurance: 'acceptsInsurance',
  verified: 'verified',
  verificationDocs: 'verificationDocs',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AvailabilityScalarFieldEnum = {
  id: 'id',
  psychiatristId: 'psychiatristId',
  dayOfWeek: 'dayOfWeek',
  startTime: 'startTime',
  endTime: 'endTime',
  isAvailable: 'isAvailable',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  psychiatristId: 'psychiatristId',
  scheduledAt: 'scheduledAt',
  duration: 'duration',
  status: 'status',
  jitsiRoomId: 'jitsiRoomId',
  sessionNotes: 'sessionNotes',
  patientNotes: 'patientNotes',
  paymentId: 'paymentId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  startedAt: 'startedAt',
  endedAt: 'endedAt'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  sessionId: 'sessionId',
  content: 'content',
  messageType: 'messageType',
  fileUrl: 'fileUrl',
  fileName: 'fileName',
  fileSize: 'fileSize',
  isRead: 'isRead',
  readAt: 'readAt',
  isEdited: 'isEdited',
  editedAt: 'editedAt',
  isFlagged: 'isFlagged',
  flaggedReason: 'flaggedReason',
  moderatedBy: 'moderatedBy',
  moderatedAt: 'moderatedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  amount: 'amount',
  currency: 'currency',
  paymentType: 'paymentType',
  status: 'status',
  stripePaymentIntentId: 'stripePaymentIntentId',
  stripeChargeId: 'stripeChargeId',
  subscriptionId: 'subscriptionId',
  description: 'description',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  planName: 'planName',
  amount: 'amount',
  currency: 'currency',
  billingCycle: 'billingCycle',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripeCustomerId: 'stripeCustomerId',
  status: 'status',
  currentPeriodStart: 'currentPeriodStart',
  currentPeriodEnd: 'currentPeriodEnd',
  cancelAtPeriodEnd: 'cancelAtPeriodEnd',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ForumPostScalarFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  title: 'title',
  content: 'content',
  category: 'category',
  tags: 'tags',
  isAnonymous: 'isAnonymous',
  status: 'status',
  aiAnalyzed: 'aiAnalyzed',
  aiSentiment: 'aiSentiment',
  aiRiskLevel: 'aiRiskLevel',
  aiSuggestions: 'aiSuggestions',
  isFlagged: 'isFlagged',
  flaggedReason: 'flaggedReason',
  moderatedBy: 'moderatedBy',
  moderatedAt: 'moderatedAt',
  viewCount: 'viewCount',
  likeCount: 'likeCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ForumCommentScalarFieldEnum = {
  id: 'id',
  postId: 'postId',
  authorId: 'authorId',
  parentId: 'parentId',
  content: 'content',
  isAnonymous: 'isAnonymous',
  status: 'status',
  isFlagged: 'isFlagged',
  flaggedReason: 'flaggedReason',
  moderatedBy: 'moderatedBy',
  moderatedAt: 'moderatedAt',
  likeCount: 'likeCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReviewScalarFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  targetId: 'targetId',
  sessionId: 'sessionId',
  rating: 'rating',
  title: 'title',
  content: 'content',
  isAnonymous: 'isAnonymous',
  isApproved: 'isApproved',
  moderatedBy: 'moderatedBy',
  moderatedAt: 'moderatedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ModerationActionScalarFieldEnum = {
  id: 'id',
  moderatorId: 'moderatorId',
  targetUserId: 'targetUserId',
  targetPostId: 'targetPostId',
  targetMessageId: 'targetMessageId',
  action: 'action',
  reason: 'reason',
  duration: 'duration',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.AIInteractionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  interactionType: 'interactionType',
  inputData: 'inputData',
  outputData: 'outputData',
  model: 'model',
  confidence: 'confidence',
  processingTime: 'processingTime',
  userFeedback: 'userFeedback',
  feedbackNotes: 'feedbackNotes',
  createdAt: 'createdAt'
};

exports.Prisma.EmergencyContactScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  relationship: 'relationship',
  phoneNumber: 'phoneNumber',
  email: 'email',
  isPrimary: 'isPrimary',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CrisisResourceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  phoneNumber: 'phoneNumber',
  website: 'website',
  region: 'region',
  category: 'category',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt'
};

exports.Prisma.SystemSettingScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  emailVerificationToken: 'emailVerificationToken',
  passwordResetToken: 'passwordResetToken',
  twoFactorSecret: 'twoFactorSecret',
  profileImage: 'profileImage',
  phoneNumber: 'phoneNumber',
  timezone: 'timezone'
};

exports.Prisma.PatientProfileOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  emergencyContact: 'emergencyContact',
  medicalHistory: 'medicalHistory',
  currentMedications: 'currentMedications',
  allergies: 'allergies',
  preferredLanguages: 'preferredLanguages',
  insuranceInfo: 'insuranceInfo'
};

exports.Prisma.PsychiatristProfileOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  licenseNumber: 'licenseNumber',
  specialties: 'specialties',
  bio: 'bio',
  education: 'education',
  experience: 'experience',
  languages: 'languages',
  verificationDocs: 'verificationDocs'
};

exports.Prisma.AvailabilityOrderByRelevanceFieldEnum = {
  id: 'id',
  psychiatristId: 'psychiatristId',
  startTime: 'startTime',
  endTime: 'endTime'
};

exports.Prisma.SessionOrderByRelevanceFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  psychiatristId: 'psychiatristId',
  jitsiRoomId: 'jitsiRoomId',
  sessionNotes: 'sessionNotes',
  patientNotes: 'patientNotes',
  paymentId: 'paymentId'
};

exports.Prisma.MessageOrderByRelevanceFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  sessionId: 'sessionId',
  content: 'content',
  fileUrl: 'fileUrl',
  fileName: 'fileName',
  flaggedReason: 'flaggedReason',
  moderatedBy: 'moderatedBy'
};

exports.Prisma.PaymentOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  currency: 'currency',
  stripePaymentIntentId: 'stripePaymentIntentId',
  stripeChargeId: 'stripeChargeId',
  subscriptionId: 'subscriptionId',
  description: 'description',
  metadata: 'metadata'
};

exports.Prisma.SubscriptionOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  planName: 'planName',
  currency: 'currency',
  billingCycle: 'billingCycle',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripeCustomerId: 'stripeCustomerId',
  status: 'status'
};

exports.Prisma.ForumPostOrderByRelevanceFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  title: 'title',
  content: 'content',
  category: 'category',
  tags: 'tags',
  aiSentiment: 'aiSentiment',
  aiRiskLevel: 'aiRiskLevel',
  aiSuggestions: 'aiSuggestions',
  flaggedReason: 'flaggedReason',
  moderatedBy: 'moderatedBy'
};

exports.Prisma.ForumCommentOrderByRelevanceFieldEnum = {
  id: 'id',
  postId: 'postId',
  authorId: 'authorId',
  parentId: 'parentId',
  content: 'content',
  flaggedReason: 'flaggedReason',
  moderatedBy: 'moderatedBy'
};

exports.Prisma.ReviewOrderByRelevanceFieldEnum = {
  id: 'id',
  authorId: 'authorId',
  targetId: 'targetId',
  sessionId: 'sessionId',
  title: 'title',
  content: 'content',
  moderatedBy: 'moderatedBy'
};

exports.Prisma.ModerationActionOrderByRelevanceFieldEnum = {
  id: 'id',
  moderatorId: 'moderatorId',
  targetUserId: 'targetUserId',
  targetPostId: 'targetPostId',
  targetMessageId: 'targetMessageId',
  action: 'action',
  reason: 'reason',
  notes: 'notes'
};

exports.Prisma.AIInteractionOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  interactionType: 'interactionType',
  inputData: 'inputData',
  outputData: 'outputData',
  model: 'model',
  userFeedback: 'userFeedback',
  feedbackNotes: 'feedbackNotes'
};

exports.Prisma.EmergencyContactOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  relationship: 'relationship',
  phoneNumber: 'phoneNumber',
  email: 'email'
};

exports.Prisma.CrisisResourceOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  phoneNumber: 'phoneNumber',
  website: 'website',
  region: 'region',
  category: 'category'
};

exports.Prisma.AuditLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.SystemSettingOrderByRelevanceFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description'
};
exports.UserRole = exports.$Enums.UserRole = {
  PATIENT: 'PATIENT',
  PSYCHIATRIST: 'PSYCHIATRIST',
  MODERATOR: 'MODERATOR',
  ADMIN: 'ADMIN'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  SUSPENDED: 'SUSPENDED',
  BANNED: 'BANNED',
  PENDING_APPROVAL: 'PENDING_APPROVAL'
};

exports.SessionStatus = exports.$Enums.SessionStatus = {
  SCHEDULED: 'SCHEDULED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  NO_SHOW: 'NO_SHOW'
};

exports.MessageType = exports.$Enums.MessageType = {
  TEXT: 'TEXT',
  IMAGE: 'IMAGE',
  FILE: 'FILE',
  SYSTEM: 'SYSTEM'
};

exports.PaymentType = exports.$Enums.PaymentType = {
  SESSION: 'SESSION',
  SUBSCRIPTION: 'SUBSCRIPTION'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED'
};

exports.PostStatus = exports.$Enums.PostStatus = {
  ACTIVE: 'ACTIVE',
  HIDDEN: 'HIDDEN',
  DELETED: 'DELETED',
  FLAGGED: 'FLAGGED'
};

exports.Prisma.ModelName = {
  User: 'User',
  PatientProfile: 'PatientProfile',
  PsychiatristProfile: 'PsychiatristProfile',
  Availability: 'Availability',
  Session: 'Session',
  Message: 'Message',
  Payment: 'Payment',
  Subscription: 'Subscription',
  ForumPost: 'ForumPost',
  ForumComment: 'ForumComment',
  Review: 'Review',
  ModerationAction: 'ModerationAction',
  AIInteraction: 'AIInteraction',
  EmergencyContact: 'EmergencyContact',
  CrisisResource: 'CrisisResource',
  AuditLog: 'AuditLog',
  SystemSetting: 'SystemSetting'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
