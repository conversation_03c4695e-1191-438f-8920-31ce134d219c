
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'; // Zap as a fallback for AI analysis, Smile for growth

const steps = [
  {
    icon: <BookOpen className="h-10 w-10 text-primary" />,
    title: 'Reflect & Record',
    description: 'Write in your private journal. Express your thoughts and feelings freely and securely.',
  },
  {
    icon: <Brain className="h-10 w-10 text-primary" />, // Or Zap
    title: 'Unlock Insights',
    description: 'Let our AI gently analyze your entries to reveal mood patterns, potential triggers, and areas for growth.',
  },
  {
    icon: <UserCheck className="h-10 w-10 text-primary" />, // Using UserCheck for a more "thrive" feel
    title: '<PERSON>row & Thrive',
    description: 'Utilize personalized recommendations, curated resources, and community support to cultivate a healthier, happier you.',
  },
];

export function HowItWorksSection() {
  return (
    <section id="how-it-works" className="py-16 md:py-24 bg-secondary/10">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Embark on Your Wellness Journey
          </h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            Getting started with WELL is simple and intuitive. Follow these steps to begin transforming your mental well-being.
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {steps.map((step, index) => (
            <Card key={step.title} className="text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="items-center">
                <div className="bg-primary/10 p-4 rounded-full mb-4">
                  {step.icon}
                </div>
                <CardTitle className="text-2xl font-semibold">
                  <span className="text-primary mr-2">{index + 1}.</span>
                  {step.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{step.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

