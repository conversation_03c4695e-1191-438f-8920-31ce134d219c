import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { UserRole } from '@prisma/client';
import { prisma } from '../../server';
import { JWTPayload, SocketUser, ChatMessage, NotificationData } from '../types';

interface AuthenticatedSocket extends Socket {
  user?: {
    id: string;
    email: string;
    role: UserRole;
  };
}

// Store connected users
const connectedUsers = new Map<string, SocketUser>();
const userSockets = new Map<string, string>(); // userId -> socketId

export const initializeSocketHandlers = (io: SocketIOServer) => {
  // Authentication middleware for Socket.IO
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      if (!process.env.JWT_SECRET) {
        return next(new Error('JWT_SECRET not configured'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload;
      
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          role: true,
          status: true,
        },
      });

      if (!user || user.status !== 'ACTIVE') {
        return next(new Error('Invalid user or inactive account'));
      }

      socket.user = {
        id: user.id,
        email: user.email,
        role: user.role,
      };

      next();
    } catch (error) {
      next(new Error('Invalid authentication token'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    if (!socket.user) return;

    const userId = socket.user.id;
    console.log(`User ${userId} connected with socket ${socket.id}`);

    // Store user connection
    connectedUsers.set(socket.id, {
      userId,
      socketId: socket.id,
      role: socket.user.role,
      isOnline: true,
      lastSeen: new Date(),
    });

    userSockets.set(userId, socket.id);

    // Join user to their personal room
    socket.join(`user:${userId}`);

    // Join role-based rooms
    socket.join(`role:${socket.user.role}`);

    // Emit user online status
    socket.broadcast.emit('user:online', { userId, isOnline: true });

    // Handle joining session rooms
    socket.on('session:join', async (data: { sessionId: string }) => {
      try {
        const session = await prisma.session.findUnique({
          where: { id: data.sessionId },
          include: {
            patient: { select: { id: true, firstName: true, lastName: true } },
            psychiatrist: { select: { id: true, firstName: true, lastName: true } },
          },
        });

        if (!session) {
          socket.emit('error', { message: 'Session not found' });
          return;
        }

        // Check if user is part of this session
        if (session.patientId !== userId && session.psychiatristId !== userId) {
          socket.emit('error', { message: 'Unauthorized to join this session' });
          return;
        }

        socket.join(`session:${data.sessionId}`);
        
        // Notify other participants
        socket.to(`session:${data.sessionId}`).emit('session:user_joined', {
          userId,
          userName: `${socket.user!.email}`,
        });

        socket.emit('session:joined', { sessionId: data.sessionId });
      } catch (error) {
        console.error('Error joining session:', error);
        socket.emit('error', { message: 'Failed to join session' });
      }
    });

    // Handle leaving session rooms
    socket.on('session:leave', (data: { sessionId: string }) => {
      socket.leave(`session:${data.sessionId}`);
      socket.to(`session:${data.sessionId}`).emit('session:user_left', {
        userId,
      });
    });

    // Handle chat messages
    socket.on('message:send', async (data: {
      receiverId?: string;
      sessionId?: string;
      content: string;
      messageType?: 'TEXT' | 'IMAGE' | 'FILE';
    }) => {
      try {
        // Validate message
        if (!data.content || data.content.trim().length === 0) {
          socket.emit('error', { message: 'Message content is required' });
          return;
        }

        // Create message in database
        const message = await prisma.message.create({
          data: {
            senderId: userId,
            receiverId: data.receiverId,
            sessionId: data.sessionId,
            content: data.content.trim(),
            messageType: data.messageType || 'TEXT',
          },
          include: {
            sender: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                profileImage: true,
              },
            },
          },
        });

        const chatMessage: ChatMessage = {
          id: message.id,
          senderId: message.senderId,
          receiverId: message.receiverId,
          sessionId: message.sessionId,
          content: message.content,
          messageType: message.messageType,
          timestamp: message.createdAt,
          isRead: false,
        };

        // Send to appropriate recipients
        if (data.sessionId) {
          // Session message - send to all session participants
          io.to(`session:${data.sessionId}`).emit('message:received', {
            ...chatMessage,
            sender: message.sender,
          });
        } else if (data.receiverId) {
          // Direct message
          const receiverSocketId = userSockets.get(data.receiverId);
          if (receiverSocketId) {
            io.to(receiverSocketId).emit('message:received', {
              ...chatMessage,
              sender: message.sender,
            });
          }
          
          // Send confirmation to sender
          socket.emit('message:sent', { messageId: message.id });
        }

        // TODO: Implement AI content moderation here
        // TODO: Check for crisis keywords and alert if necessary

      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing:start', (data: { receiverId?: string; sessionId?: string }) => {
      if (data.sessionId) {
        socket.to(`session:${data.sessionId}`).emit('typing:start', { userId });
      } else if (data.receiverId) {
        const receiverSocketId = userSockets.get(data.receiverId);
        if (receiverSocketId) {
          io.to(receiverSocketId).emit('typing:start', { userId });
        }
      }
    });

    socket.on('typing:stop', (data: { receiverId?: string; sessionId?: string }) => {
      if (data.sessionId) {
        socket.to(`session:${data.sessionId}`).emit('typing:stop', { userId });
      } else if (data.receiverId) {
        const receiverSocketId = userSockets.get(data.receiverId);
        if (receiverSocketId) {
          io.to(receiverSocketId).emit('typing:stop', { userId });
        }
      }
    });

    // Handle message read receipts
    socket.on('message:read', async (data: { messageId: string }) => {
      try {
        await prisma.message.update({
          where: { id: data.messageId },
          data: { isRead: true, readAt: new Date() },
        });

        // Notify sender
        const message = await prisma.message.findUnique({
          where: { id: data.messageId },
          select: { senderId: true },
        });

        if (message) {
          const senderSocketId = userSockets.get(message.senderId);
          if (senderSocketId) {
            io.to(senderSocketId).emit('message:read', {
              messageId: data.messageId,
              readBy: userId,
            });
          }
        }
      } catch (error) {
        console.error('Error marking message as read:', error);
      }
    });

    // Handle session status updates
    socket.on('session:status', async (data: {
      sessionId: string;
      status: 'started' | 'ended';
    }) => {
      try {
        const session = await prisma.session.findUnique({
          where: { id: data.sessionId },
        });

        if (!session) {
          socket.emit('error', { message: 'Session not found' });
          return;
        }

        // Check if user is part of this session
        if (session.patientId !== userId && session.psychiatristId !== userId) {
          socket.emit('error', { message: 'Unauthorized to update session status' });
          return;
        }

        // Update session status
        const updateData: any = {};
        if (data.status === 'started') {
          updateData.status = 'IN_PROGRESS';
          updateData.startedAt = new Date();
        } else if (data.status === 'ended') {
          updateData.status = 'COMPLETED';
          updateData.endedAt = new Date();
        }

        await prisma.session.update({
          where: { id: data.sessionId },
          data: updateData,
        });

        // Notify all session participants
        io.to(`session:${data.sessionId}`).emit('session:status_updated', {
          sessionId: data.sessionId,
          status: data.status,
          timestamp: new Date(),
        });

      } catch (error) {
        console.error('Error updating session status:', error);
        socket.emit('error', { message: 'Failed to update session status' });
      }
    });

    // Handle notifications
    socket.on('notification:send', (data: {
      targetUserId: string;
      notification: NotificationData;
    }) => {
      const targetSocketId = userSockets.get(data.targetUserId);
      if (targetSocketId) {
        io.to(targetSocketId).emit('notification:received', data.notification);
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`User ${userId} disconnected`);
      
      // Remove from connected users
      connectedUsers.delete(socket.id);
      userSockets.delete(userId);

      // Emit user offline status
      socket.broadcast.emit('user:offline', { userId, isOnline: false });
    });

    // Send initial connection confirmation
    socket.emit('connected', {
      userId,
      socketId: socket.id,
      timestamp: new Date(),
    });
  });

  // Helper function to send notification to user
  const sendNotificationToUser = (userId: string, notification: NotificationData) => {
    const socketId = userSockets.get(userId);
    if (socketId) {
      io.to(socketId).emit('notification:received', notification);
    }
  };

  // Helper function to get online users
  const getOnlineUsers = (): SocketUser[] => {
    return Array.from(connectedUsers.values());
  };

  // Helper function to check if user is online
  const isUserOnline = (userId: string): boolean => {
    return userSockets.has(userId);
  };

  // Export helper functions for use in other parts of the application
  return {
    sendNotificationToUser,
    getOnlineUsers,
    isUserOnline,
  };
};
