import { Router } from 'express';
import { z } from 'zod';
import { UserRole, SessionStatus } from '@prisma/client';
import { prisma } from '../../server';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { requireRole, requireEmailVerification } from '../middleware/auth';
import { AuthUtils } from '../utils/auth';

const router = Router();

// Validation schemas
const createSessionSchema = z.object({
  psychiatristId: z.string().cuid('Invalid psychiatrist ID'),
  scheduledAt: z.string().datetime('Invalid date format'),
  duration: z.number().min(15).max(180).optional().default(60),
  notes: z.string().optional(),
});

const updateSessionSchema = z.object({
  status: z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW']).optional(),
  sessionNotes: z.string().optional(),
  patientNotes: z.string().optional(),
});

const sessionQuerySchema = z.object({
  page: z.string().transform(Number).optional().default(1),
  limit: z.string().transform(Number).optional().default(10),
  status: z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

// Create a new session (patients only)
router.post('/', requireEmailVerification, requireRole([UserRole.PATIENT]), asyncHandler(async (req, res) => {
  const validatedData = createSessionSchema.parse(req.body);
  const patientId = req.user!.id;

  // Check if psychiatrist exists and is verified
  const psychiatrist = await prisma.user.findUnique({
    where: { id: validatedData.psychiatristId },
    include: { psychiatristProfile: true },
  });

  if (!psychiatrist || psychiatrist.role !== UserRole.PSYCHIATRIST) {
    throw new CustomError('Psychiatrist not found', 404);
  }

  if (!psychiatrist.psychiatristProfile?.verified) {
    throw new CustomError('Psychiatrist is not verified', 400);
  }

  if (psychiatrist.status !== 'ACTIVE') {
    throw new CustomError('Psychiatrist is not available', 400);
  }

  // Check for scheduling conflicts
  const scheduledAt = new Date(validatedData.scheduledAt);
  const endTime = new Date(scheduledAt.getTime() + validatedData.duration * 60000);

  const conflictingSessions = await prisma.session.count({
    where: {
      psychiatristId: validatedData.psychiatristId,
      status: { in: ['SCHEDULED', 'IN_PROGRESS'] },
      OR: [
        {
          scheduledAt: { lte: scheduledAt },
          AND: {
            scheduledAt: {
              gte: new Date(scheduledAt.getTime() - validatedData.duration * 60000)
            }
          }
        },
        {
          scheduledAt: { gte: scheduledAt, lte: endTime }
        }
      ]
    }
  });

  if (conflictingSessions > 0) {
    throw new CustomError('Time slot is not available', 409);
  }

  // Generate Jitsi room ID
  const jitsiRoomId = AuthUtils.generateJitsiRoomId(AuthUtils.generateSessionId());

  // Create session
  const session = await prisma.session.create({
    data: {
      patientId,
      psychiatristId: validatedData.psychiatristId,
      scheduledAt,
      duration: validatedData.duration,
      jitsiRoomId,
      patientNotes: validatedData.notes,
      status: SessionStatus.SCHEDULED,
    },
    include: {
      patient: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      psychiatrist: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          psychiatristProfile: {
            select: {
              hourlyRate: true,
              specialties: true,
            },
          },
        },
      },
    },
  });

  res.status(201).json({
    success: true,
    message: 'Session created successfully',
    data: session,
  });
}));

// Get sessions for current user
router.get('/', requireEmailVerification, asyncHandler(async (req, res) => {
  const query = sessionQuerySchema.parse(req.query);
  const userId = req.user!.id;
  const userRole = req.user!.role;

  const where: any = {};

  // Filter by user role
  if (userRole === UserRole.PATIENT) {
    where.patientId = userId;
  } else if (userRole === UserRole.PSYCHIATRIST) {
    where.psychiatristId = userId;
  } else {
    // Admins and moderators can see all sessions
  }

  // Apply filters
  if (query.status) {
    where.status = query.status;
  }

  if (query.startDate || query.endDate) {
    where.scheduledAt = {};
    if (query.startDate) {
      where.scheduledAt.gte = new Date(query.startDate);
    }
    if (query.endDate) {
      where.scheduledAt.lte = new Date(query.endDate);
    }
  }

  const [sessions, total] = await Promise.all([
    prisma.session.findMany({
      where,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        psychiatrist: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            psychiatristProfile: {
              select: {
                hourlyRate: true,
                specialties: true,
              },
            },
          },
        },
        payment: {
          select: {
            id: true,
            amount: true,
            status: true,
          },
        },
      },
      orderBy: { scheduledAt: 'desc' },
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    }),
    prisma.session.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      sessions,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
        hasNext: query.page * query.limit < total,
        hasPrev: query.page > 1,
      },
    },
  });
}));

// Get specific session
router.get('/:sessionId', requireEmailVerification, asyncHandler(async (req, res) => {
  const { sessionId } = req.params;
  const userId = req.user!.id;
  const userRole = req.user!.role;

  const session = await prisma.session.findUnique({
    where: { id: sessionId },
    include: {
      patient: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      psychiatrist: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          psychiatristProfile: {
            select: {
              hourlyRate: true,
              specialties: true,
              bio: true,
            },
          },
        },
      },
      payment: {
        select: {
          id: true,
          amount: true,
          status: true,
          stripePaymentIntentId: true,
        },
      },
      messages: {
        include: {
          sender: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: { createdAt: 'asc' },
      },
    },
  });

  if (!session) {
    throw new CustomError('Session not found', 404);
  }

  // Check permissions
  const canAccess = 
    userRole === UserRole.ADMIN ||
    userRole === UserRole.MODERATOR ||
    session.patientId === userId ||
    session.psychiatristId === userId;

  if (!canAccess) {
    throw new CustomError('Access denied', 403);
  }

  res.json({
    success: true,
    data: session,
  });
}));

// Update session
router.put('/:sessionId', requireEmailVerification, asyncHandler(async (req, res) => {
  const { sessionId } = req.params;
  const validatedData = updateSessionSchema.parse(req.body);
  const userId = req.user!.id;
  const userRole = req.user!.role;

  const session = await prisma.session.findUnique({
    where: { id: sessionId },
  });

  if (!session) {
    throw new CustomError('Session not found', 404);
  }

  // Check permissions
  const canUpdate = 
    userRole === UserRole.ADMIN ||
    session.patientId === userId ||
    session.psychiatristId === userId;

  if (!canUpdate) {
    throw new CustomError('Access denied', 403);
  }

  // Validate status transitions
  if (validatedData.status) {
    const validTransitions: Record<SessionStatus, SessionStatus[]> = {
      SCHEDULED: ['IN_PROGRESS', 'CANCELLED', 'NO_SHOW'],
      IN_PROGRESS: ['COMPLETED', 'CANCELLED'],
      COMPLETED: [], // Cannot change from completed
      CANCELLED: [], // Cannot change from cancelled
      NO_SHOW: [], // Cannot change from no-show
    };

    if (!validTransitions[session.status].includes(validatedData.status)) {
      throw new CustomError(`Cannot change status from ${session.status} to ${validatedData.status}`, 400);
    }
  }

  const updateData: any = {};

  if (validatedData.status) {
    updateData.status = validatedData.status;
    
    if (validatedData.status === SessionStatus.IN_PROGRESS && !session.startedAt) {
      updateData.startedAt = new Date();
    }
    
    if (validatedData.status === SessionStatus.COMPLETED && !session.endedAt) {
      updateData.endedAt = new Date();
    }
  }

  // Only psychiatrists can update session notes
  if (validatedData.sessionNotes && session.psychiatristId === userId) {
    updateData.sessionNotes = validatedData.sessionNotes;
  }

  // Only patients can update patient notes
  if (validatedData.patientNotes && session.patientId === userId) {
    updateData.patientNotes = validatedData.patientNotes;
  }

  const updatedSession = await prisma.session.update({
    where: { id: sessionId },
    data: updateData,
    include: {
      patient: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      psychiatrist: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
    },
  });

  res.json({
    success: true,
    message: 'Session updated successfully',
    data: updatedSession,
  });
}));

// Cancel session
router.delete('/:sessionId', requireEmailVerification, asyncHandler(async (req, res) => {
  const { sessionId } = req.params;
  const userId = req.user!.id;

  const session = await prisma.session.findUnique({
    where: { id: sessionId },
    include: { payment: true },
  });

  if (!session) {
    throw new CustomError('Session not found', 404);
  }

  // Check permissions
  if (session.patientId !== userId && session.psychiatristId !== userId) {
    throw new CustomError('Access denied', 403);
  }

  // Can only cancel scheduled sessions
  if (session.status !== SessionStatus.SCHEDULED) {
    throw new CustomError('Can only cancel scheduled sessions', 400);
  }

  // Check cancellation policy (e.g., 24 hours before)
  const hoursUntilSession = (session.scheduledAt.getTime() - Date.now()) / (1000 * 60 * 60);
  if (hoursUntilSession < 24) {
    throw new CustomError('Cannot cancel session less than 24 hours before scheduled time', 400);
  }

  await prisma.session.update({
    where: { id: sessionId },
    data: { status: SessionStatus.CANCELLED },
  });

  // TODO: Handle payment refund if applicable

  res.json({
    success: true,
    message: 'Session cancelled successfully',
  });
}));

export default router;
