
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

export function HeroSection() {
  return (
    <section className="py-16 md:py-24 lg:py-32 bg-gradient-to-b from-background to-secondary/10">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <div className="max-w-xl text-center lg:text-left">
            <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-5xl md:text-6xl">
              Find Your Calm. <span className="text-primary">Nurture Your Mind.</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              WELL is your personal companion for mental wellness, offering AI-driven insights, a supportive community, and guided journaling to help you flourish.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6 lg:justify-start">
              <Button size="lg" asChild>
                <Link href="/join">Start Your Journey Free</Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="#how-it-works">Learn More</Link>
              </Button>
            </div>
          </div>
          <div className="relative aspect-[4/3] lg:aspect-video rounded-xl overflow-hidden shadow-2xl">
            <Image
              src="https://picsum.photos/seed/well-hero/1200/800"
              alt="Serene abstract image representing mental clarity for WELL app"
              layout="fill"
              objectFit="cover"
              data-ai-hint="serene abstract"
              priority
            />
             <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 via-transparent to-secondary/20"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
