
'use client';

import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { SpaceCard } from '@/components/people/space-card';
import { CreateSpaceForm } from '@/components/people/create-space-form';
import { PLACEHOLDER_SPACES } from '@/lib/constants';
import type { Space } from '@/lib/types';
import { Network, PlusCircle, Search, Filter } from 'lucide-react'; // Changed UsersRound to Network
import { Input } from '@/components/ui/input';
import { SpaceFilters, type SpaceFiltersState } from '@/components/people/space-filters';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';


export default function PeoplePage() {
  const [spaces, setSpaces] = useState<Space[]>(PLACEHOLDER_SPACES);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [filters, setFilters] = useState<SpaceFiltersState>({
    type: 'all',
    tags: [],
    sortBy: 'popularity',
  });
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);

  const handleSpaceCreated = (newSpaceData: any) => {
    // This is a simulation. In a real app, you'd refresh data or add to state.
    // For now, we just close the dialog.
    const newSpace: Space = {
        id: `space${Date.now()}`,
        name: newSpaceData.name,
        description: newSpaceData.description,
        bannerImageUrl: 'https://picsum.photos/seed/newspacebanner/800/200', // Placeholder
        profileImageUrl: 'https://picsum.photos/seed/newspaceprofile/100/100', // Placeholder
        aiBannerHint: 'abstract banner',
        aiProfileHint: 'new logo',
        tags: newSpaceData.tags ? newSpaceData.tags.split(',').map((t: string) => t.trim()) : [],
        host: { name: 'Current User', avatarUrl: 'https://picsum.photos/seed/currenthost/50/50', aiHostAvatarHint: 'user photo' },
        isSubscriptionBased: newSpaceData.spaceType === 'paid',
        followerCount: 0,
        createdAt: new Date().toISOString(),
        ...(newSpaceData.spaceType === 'paid' && { 
            subscriptionTiers: [{ id: 'default', name: 'Premium', pricePerMonth: newSpaceData.monthlyPrice || 0, features: ['Full access']}] 
        }),
    };
    setSpaces(prevSpaces => [newSpace, ...prevSpaces]);
    setIsCreateDialogOpen(false); 
  };
  
  const allTags = useMemo(() => {
    const tagSet = new Set<string>();
    spaces.forEach(space => space.tags.forEach(tag => tagSet.add(tag)));
    return Array.from(tagSet).sort();
  }, [spaces]);

  const filteredAndSortedSpaces = useMemo(() => {
    let processedSpaces = spaces.filter(space => 
      (space.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      space.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      space.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
      space.host.name.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (filters.type === 'all' || 
       (filters.type === 'free' && !space.isSubscriptionBased) ||
       (filters.type === 'paid' && space.isSubscriptionBased)) &&
      (filters.tags.length === 0 || filters.tags.some(tag => space.tags.includes(tag)))
    );

    switch (filters.sortBy) {
      case 'popularity':
        processedSpaces.sort((a, b) => b.followerCount - a.followerCount);
        break;
      case 'newest':
        processedSpaces.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'name':
        processedSpaces.sort((a,b) => a.name.localeCompare(b.name));
        break;
    }
    return processedSpaces;
  }, [spaces, searchTerm, filters]);

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-10 gap-4">
        <div className="flex items-center">
          <Network className="h-10 w-10 text-primary mr-3" /> {/* Changed UsersRound to Network */}
          <h1 className="text-3xl font-bold text-primary">People Hub</h1>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-5 w-5" /> Create New Space
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl">Create Your Wellness Space</DialogTitle>
              <DialogDescription>
                Share your expertise, build a community, and support others on their wellness journey.
              </DialogDescription>
            </DialogHeader>
            <CreateSpaceForm onSpaceCreate={handleSpaceCreated} />
          </DialogContent>
        </Dialog>
      </div>

      <p className="text-center text-muted-foreground mb-8 max-w-2xl mx-auto">
        Discover and join spaces hosted by influencers, therapists, and advocates. Engage, learn, and grow together in a supportive environment.
      </p>

      <div className="mb-8 flex flex-col md:flex-row gap-4 items-center">
        <div className="relative flex-grow w-full">
          <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search spaces by name, topic, or host..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 w-full"
          />
        </div>
        <div className="md:hidden"> {/* Mobile filter button */}
          <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="w-full">
                <Filter className="mr-2 h-4 w-4" /> Filters & Sort
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[300px] p-0">
               <SpaceFilters allTags={allTags} filters={filters} onFilterChange={setFilters} />
            </SheetContent>
          </Sheet>
        </div>
      </div>
      
      <div className="grid md:grid-cols-[280px_1fr] gap-8">
        <aside className="hidden md:block"> {/* Desktop filter sidebar */}
          <SpaceFilters allTags={allTags} filters={filters} onFilterChange={setFilters} />
        </aside>
        
        <main>
          {filteredAndSortedSpaces.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"> {/* Adjusted for potentially wider content area */}
              {filteredAndSortedSpaces.map((space) => (
                <SpaceCard key={space.id} space={space} />
              ))}
            </div>
          ) : (
            <p className="text-center text-muted-foreground py-10 col-span-full">
              No spaces found matching your criteria. Why not create one?
            </p>
          )}
        </main>
      </div>

      <div className="mt-12 p-6 bg-secondary/10 rounded-lg text-center">
        <h3 className="text-xl font-semibold text-primary mb-2">For Hosts & Creators</h3>
        <p className="text-muted-foreground mb-4">
          WELL empowers you to build and nurture your own wellness community. Share content, host live events, and connect with your audience on a deeper level.
        </p>
        <Button variant="outline">Learn More About Hosting</Button>
      </div>
    </div>
  );
}

