import { UserRole } from '@prisma/client';
import { JWTPayload, AuthResponse, AuthenticatedUser } from '../types';
export declare class AuthUtils {
    static hashPassword(password: string): Promise<string>;
    static comparePassword(password: string, hashedPassword: string): Promise<boolean>;
    static generateAccessToken(payload: JWTPayload): string;
    static generateRefreshToken(payload: JWTPayload): string;
    static verifyRefreshToken(token: string): JWTPayload;
    static generateTokenPair(user: AuthenticatedUser): {
        accessToken: string;
        refreshToken: string;
    };
    static generateEmailVerificationToken(): string;
    static generatePasswordResetToken(): string;
    static generateTwoFactorSecret(): {
        secret: string;
        qrCodeUrl: string;
    };
    static verifyTwoFactorToken(token: string, secret: string): boolean;
    static validatePassword(password: string): {
        isValid: boolean;
        errors: string[];
    };
    static validateEmail(email: string): boolean;
    static generateSessionId(): string;
    static generateJitsiRoomId(sessionId: string): string;
    static sanitizeUserData(user: any): AuthenticatedUser;
    static createAuthResponse(user: AuthenticatedUser): AuthResponse;
    static isValidRole(role: string): role is UserRole;
    static canAccessResource(userRole: UserRole, requiredRoles: UserRole[]): boolean;
    static getRoleHierarchy(): Record<UserRole, number>;
    static hasHigherRole(userRole: UserRole, targetRole: UserRole): boolean;
    static generateApiKey(): string;
    static hashApiKey(apiKey: string): string;
}
//# sourceMappingURL=auth.d.ts.map