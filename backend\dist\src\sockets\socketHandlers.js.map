{"version": 3, "file": "socketHandlers.js", "sourceRoot": "", "sources": ["../../../src/sockets/socketHandlers.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAE/B,yCAAsC;AAYtC,MAAM,cAAc,GAAG,IAAI,GAAG,EAAsB,CAAC;AACrD,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;AAEvC,MAAM,wBAAwB,GAAG,CAAC,EAAkB,EAAE,EAAE;IAE7D,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,MAA2B,EAAE,IAAI,EAAE,EAAE;QACjD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAE1C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;YAC1D,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAe,CAAC;YAExE,MAAM,IAAI,GAAG,MAAM,eAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;gBAC7B,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,IAAI;oBACV,MAAM,EAAE,IAAI;iBACb;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACtC,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,CAAC,IAAI,GAAG;gBACZ,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;YAEF,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAA2B,EAAE,EAAE;QAClD,IAAI,CAAC,MAAM,CAAC,IAAI;YAAE,OAAO;QAEzB,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,0BAA0B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAGjE,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;YAC5B,MAAM;YACN,QAAQ,EAAE,MAAM,CAAC,EAAE;YACnB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI;YACtB,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,IAAI,IAAI,EAAE;SACrB,CAAC,CAAC;QAEH,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QAGnC,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,EAAE,CAAC,CAAC;QAG9B,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAGxC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QAGjE,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAA2B,EAAE,EAAE;YAC9D,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,OAAO,CAAC,UAAU,CAAC;oBAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;oBAC7B,OAAO,EAAE;wBACP,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;wBAClE,YAAY,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;qBACxE;iBACF,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBACvD,OAAO;gBACT,CAAC;gBAGD,IAAI,OAAO,CAAC,SAAS,KAAK,MAAM,IAAI,OAAO,CAAC,cAAc,KAAK,MAAM,EAAE,CAAC;oBACtE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;oBACvE,OAAO;gBACT,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;gBAGzC,MAAM,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE;oBACjE,MAAM;oBACN,QAAQ,EAAE,GAAG,MAAM,CAAC,IAAK,CAAC,KAAK,EAAE;iBAClC,CAAC,CAAC;gBAEH,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YAC/D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBAC/C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,IAA2B,EAAE,EAAE;YACzD,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/D,MAAM;aACP,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAKhC,EAAE,EAAE;YACH,IAAI,CAAC;gBAEH,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC,CAAC;oBACjE,OAAO;gBACT,CAAC;gBAGD,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC1C,IAAI,EAAE;wBACJ,QAAQ,EAAE,MAAM;wBAChB,UAAU,EAAE,IAAI,CAAC,UAAU;wBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;wBAC5B,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,MAAM;qBACxC;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;gCACd,YAAY,EAAE,IAAI;6BACnB;yBACF;qBACF;iBACF,CAAC,CAAC;gBAEH,MAAM,WAAW,GAAgB;oBAC/B,EAAE,EAAE,OAAO,CAAC,EAAE;oBACd,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,MAAM,EAAE,KAAK;iBACd,CAAC;gBAGF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBAEnB,EAAE,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE;wBAC1D,GAAG,WAAW;wBACd,MAAM,EAAE,OAAO,CAAC,MAAM;qBACvB,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAE3B,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC1D,IAAI,gBAAgB,EAAE,CAAC;wBACrB,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE;4BAC/C,GAAG,WAAW;4BACd,MAAM,EAAE,OAAO,CAAC,MAAM;yBACvB,CAAC,CAAC;oBACL,CAAC;oBAGD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzD,CAAC;YAKH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBAC/C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAiD,EAAE,EAAE;YAC9E,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1E,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC1D,IAAI,gBAAgB,EAAE,CAAC;oBACrB,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAiD,EAAE,EAAE;YAC7E,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACzE,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC3B,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC1D,IAAI,gBAAgB,EAAE,CAAC;oBACrB,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAA2B,EAAE,EAAE;YAC9D,IAAI,CAAC;gBACH,MAAM,eAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC1B,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;oBAC7B,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE,EAAE;iBAC3C,CAAC,CAAC;gBAGH,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,OAAO,CAAC,UAAU,CAAC;oBAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;oBAC7B,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAC3B,CAAC,CAAC;gBAEH,IAAI,OAAO,EAAE,CAAC;oBACZ,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACzD,IAAI,cAAc,EAAE,CAAC;wBACnB,EAAE,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE;4BACzC,SAAS,EAAE,IAAI,CAAC,SAAS;4BACzB,MAAM,EAAE,MAAM;yBACf,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACzD,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAGlC,EAAE,EAAE;YACH,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,eAAM,CAAC,OAAO,CAAC,UAAU,CAAC;oBAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;iBAC9B,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBACvD,OAAO;gBACT,CAAC;gBAGD,IAAI,OAAO,CAAC,SAAS,KAAK,MAAM,IAAI,OAAO,CAAC,cAAc,KAAK,MAAM,EAAE,CAAC;oBACtE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC,CAAC;oBAC3E,OAAO;gBACT,CAAC;gBAGD,MAAM,UAAU,GAAQ,EAAE,CAAC;gBAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC9B,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC;oBAClC,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;gBACpC,CAAC;qBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;oBACnC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC;oBAChC,UAAU,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBAClC,CAAC;gBAED,MAAM,eAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC1B,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;oBAC7B,IAAI,EAAE,UAAU;iBACjB,CAAC,CAAC;gBAGH,EAAE,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,wBAAwB,EAAE;oBAChE,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACvD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,IAG/B,EAAE,EAAE;YACH,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,cAAc,EAAE,CAAC;gBACnB,EAAE,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACzE,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,eAAe,CAAC,CAAC;YAG3C,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAG3B,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;YACvB,MAAM;YACN,QAAQ,EAAE,MAAM,CAAC,EAAE;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAGH,MAAM,sBAAsB,GAAG,CAAC,MAAc,EAAE,YAA8B,EAAE,EAAE;QAChF,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,QAAQ,EAAE,CAAC;YACb,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC,CAAC;IAGF,MAAM,cAAc,GAAG,GAAiB,EAAE;QACxC,OAAO,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC,CAAC;IAGF,MAAM,YAAY,GAAG,CAAC,MAAc,EAAW,EAAE;QAC/C,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC,CAAC;IAGF,OAAO;QACL,sBAAsB;QACtB,cAAc;QACd,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAxVW,QAAA,wBAAwB,4BAwVnC"}