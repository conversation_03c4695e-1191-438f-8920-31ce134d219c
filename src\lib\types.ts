
export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  mood: Mood;
  date: string; // ISO string
}

export type Mood = 'happy' | 'sad' | 'anxious' | 'calm' | 'neutral' | 'angry' | 'excited';

export const MOOD_OPTIONS: Mood[] = ['happy', 'sad', 'anxious', 'calm', 'neutral', 'angry', 'excited'];

export interface Resource {
  id: string;
  type: 'article' | 'video' | 'guide';
  title: string;
  description: string;
  link: string;
  imageUrl?: string;
  aiHint?: string; // For picsum images
}

export interface ForumPost {
  id: string;
  title: string;
  content: string;
  author: string;
  avatarUrl?: string;
  aiHint?: string; // For picsum images
  date: string; // ISO string
  replies?: ForumPost[]; // Optional: for threaded discussions
}

// Therapy Feature Types
export interface TherapistProfile {
  id: string;
  name: string;
  specialties: string[];
  bio: string;
  avatarUrl: string;
  aiHint?: string;
  availability: string;
  hourlyRate?: number;
}

export interface GuidedSession {
  id: string;
  title: string;
  category: string;
  type: 'audio' | 'video';
  duration: string;
  description: string;
  thumbnailUrl: string;
  aiHint?: string;
  sourceUrl: string;
}

export interface CBTExercise {
  id: string;
  title: string;
  description: string;
  category: string;
  interactive: boolean;
  iconName?: string; // Lucide icon name as string
}

export interface CrisisResourceItem {
  id: string;
  name: string;
  contact: string;
  description: string;
  type: 'phone' | 'link';
}

export interface ProgressData {
  sessionsCompleted: number;
  exercisesDone: number;
  moodAverage?: number;
  journalStreak: number;
}

// People Hub Feature Types
export interface SpaceSubscriptionTier {
  id: string;
  name: 'Free' | 'Supporter' | 'Premium'; // Example tiers
  pricePerMonth: number; // 0 for Free tier
  features: string[];
}

export interface Space {
  id: string;
  name: string;
  description: string;
  bannerImageUrl: string;
  profileImageUrl: string;
  aiBannerHint?: string;
  aiProfileHint?: string;
  tags: string[];
  host: {
    name: string;
    avatarUrl: string;
    aiHostAvatarHint?: string;
  };
  isSubscriptionBased: boolean;
  subscriptionTiers?: SpaceSubscriptionTier[]; // Only if isSubscriptionBased is true
  followerCount: number;
  createdAt: string; // ISO string, added for sorting
}

export interface SpaceContent {
  id: string;
  spaceId: string;
  title: string;
  type: 'pdf' | 'video' | 'guide' | 'meditation' | 'article';
  description?: string;
  url: string; // or some content identifier
  uploadDate: string; // ISO String
  category?: string;
}

export interface SpaceEvent {
  id: string;
  spaceId: string;
  title: string;
  description: string;
  dateTime: string; // ISO String for start time
  durationMinutes: number;
  platform: 'Jitsi' | 'Zoom' | 'Other'; // Example platforms
  meetingLink?: string;
  rsvps: number;
  maxAttendees?: number;
}

export interface SpaceDiscussionPost {
  id: string;
  spaceId: string;
  authorName: string; // Or userId and fetch details
  authorAvatarUrl?: string;
  aiAuthorAvatarHint?: string;
  content: string;
  timestamp: string; // ISO String
  isPinned?: boolean;
  reactions?: { [emoji: string]: number }; // e.g., { '👍': 10, '❤️': 5 }
  repliesCount?: number;
}

// Therapist Dashboard Specific Types
export interface PlaceholderClient {
  id: string;
  name: string;
  avatarUrl: string;
  aiAvatarHint?: string;
  lastSession: string; // Date string or relative time
  nextSession?: string; // Date string or "Not Scheduled"
  status: 'Active' | 'On Hold' | 'Inactive';
}

export interface PlaceholderAppointment {
  id: string;
  time: string; // e.g., "10:00 AM"
  clientName: string;
  duration: string; // e.g., "50 min"
  type: 'Initial Consultation' | 'Follow-up' | 'Group Session';
}
