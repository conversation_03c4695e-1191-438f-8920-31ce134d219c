import { Router } from 'express';
import { z } from 'zod';
import { UserRole, UserStatus } from '@prisma/client';
import { prisma } from '../../server';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { requireRole } from '../middleware/auth';

const router = Router();

// All admin routes require ADMIN role
router.use(requireRole([UserRole.ADMIN]));

// Get system statistics
router.get('/stats', asyncHandler(async (req, res) => {
  const [
    totalUsers,
    totalSessions,
    totalRevenue,
    activeUsers,
    pendingApprovals,
    flaggedContent
  ] = await Promise.all([
    prisma.user.count(),
    prisma.session.count(),
    prisma.payment.aggregate({
      where: { status: 'COMPLETED' },
      _sum: { amount: true },
    }).then(result => result._sum.amount || 0),
    prisma.user.count({ where: { status: 'ACTIVE' } }),
    prisma.user.count({ 
      where: { 
        role: UserRole.PSYCHIATRIST, 
        status: 'PENDING_APPROVAL' 
      } 
    }),
    prisma.forumPost.count({ where: { isFlagged: true } }),
  ]);

  res.json({
    success: true,
    data: {
      totalUsers,
      totalSessions,
      totalRevenue,
      activeUsers,
      pendingApprovals,
      flaggedContent,
    },
  });
}));

// Get pending psychiatrist approvals
router.get('/pending-approvals', asyncHandler(async (req, res) => {
  const pendingPsychiatrists = await prisma.user.findMany({
    where: {
      role: UserRole.PSYCHIATRIST,
      status: 'PENDING_APPROVAL',
    },
    include: {
      psychiatristProfile: true,
    },
    orderBy: { createdAt: 'asc' },
  });

  res.json({
    success: true,
    data: pendingPsychiatrists,
  });
}));

// Approve/reject psychiatrist
router.put('/approve-psychiatrist/:userId', asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { approved, reason } = z.object({
    approved: z.boolean(),
    reason: z.string().optional(),
  }).parse(req.body);

  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { psychiatristProfile: true },
  });

  if (!user || user.role !== UserRole.PSYCHIATRIST) {
    throw new CustomError('Psychiatrist not found', 404);
  }

  if (user.status !== 'PENDING_APPROVAL') {
    throw new CustomError('User is not pending approval', 400);
  }

  if (approved) {
    await prisma.$transaction([
      prisma.user.update({
        where: { id: userId },
        data: { status: UserStatus.ACTIVE },
      }),
      prisma.psychiatristProfile.update({
        where: { userId },
        data: { verified: true },
      }),
    ]);
  } else {
    await prisma.user.update({
      where: { id: userId },
      data: { status: UserStatus.SUSPENDED },
    });
  }

  // TODO: Send email notification to psychiatrist

  res.json({
    success: true,
    message: `Psychiatrist ${approved ? 'approved' : 'rejected'} successfully`,
  });
}));

// Get all users with pagination
router.get('/users', asyncHandler(async (req, res) => {
  const query = z.object({
    page: z.string().transform(Number).optional().default(1),
    limit: z.string().transform(Number).optional().default(20),
    role: z.nativeEnum(UserRole).optional(),
    status: z.nativeEnum(UserStatus).optional(),
    search: z.string().optional(),
  }).parse(req.query);

  const where: any = {};

  if (query.role) where.role = query.role;
  if (query.status) where.status = query.status;
  if (query.search) {
    where.OR = [
      { email: { contains: query.search } },
      { firstName: { contains: query.search } },
      { lastName: { contains: query.search } },
    ];
  }

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        emailVerified: true,
        createdAt: true,
        lastLoginAt: true,
      },
      orderBy: { createdAt: 'desc' },
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    }),
    prisma.user.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      users,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
        hasNext: query.page * query.limit < total,
        hasPrev: query.page > 1,
      },
    },
  });
}));

// Update user status
router.put('/users/:userId/status', asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { status, reason } = z.object({
    status: z.nativeEnum(UserStatus),
    reason: z.string().optional(),
  }).parse(req.body);

  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { id: true, role: true, status: true },
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Prevent admins from suspending other admins
  if (user.role === UserRole.ADMIN && status === UserStatus.SUSPENDED) {
    throw new CustomError('Cannot suspend admin users', 403);
  }

  await prisma.user.update({
    where: { id: userId },
    data: { status },
  });

  // Log moderation action
  await prisma.moderationAction.create({
    data: {
      moderatorId: req.user!.id,
      targetUserId: userId,
      action: `status_change_${status}`,
      reason: reason || `Status changed to ${status}`,
    },
  });

  res.json({
    success: true,
    message: 'User status updated successfully',
  });
}));

// Get system settings
router.get('/settings', asyncHandler(async (req, res) => {
  const settings = await prisma.systemSetting.findMany({
    orderBy: { key: 'asc' },
  });

  res.json({
    success: true,
    data: settings,
  });
}));

// Update system setting
router.put('/settings/:key', asyncHandler(async (req, res) => {
  const { key } = req.params;
  const { value } = z.object({
    value: z.string(),
  }).parse(req.body);

  const setting = await prisma.systemSetting.upsert({
    where: { key },
    update: { value },
    create: { key, value },
  });

  res.json({
    success: true,
    message: 'Setting updated successfully',
    data: setting,
  });
}));

export default router;
