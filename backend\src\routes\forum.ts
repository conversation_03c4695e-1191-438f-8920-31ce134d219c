import { Router } from 'express';
import { z } from 'zod';
import { UserRole } from '@prisma/client';
import { prisma } from '../../server';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { requireEmailVerification, optionalAuth } from '../middleware/auth';

const router = Router();

// Validation schemas
const createPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  content: z.string().min(1, 'Content is required').max(10000, 'Content too long'),
  category: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).optional().default([]),
  isAnonymous: z.boolean().optional().default(false),
});

const postQuerySchema = z.object({
  page: z.string().transform(Number).optional().default(1),
  limit: z.string().transform(Number).optional().default(20),
  category: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['createdAt', 'viewCount', 'likeCount']).optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// Get forum posts
router.get('/posts', asyncHandler(async (req, res) => {
  const query = postQuerySchema.parse(req.query);
  
  const where: any = {
    status: 'ACTIVE',
  };

  if (query.category) {
    where.category = query.category;
  }

  if (query.search) {
    where.OR = [
      { title: { contains: query.search } },
      { content: { contains: query.search } },
    ];
  }

  const [posts, total] = await Promise.all([
    prisma.forumPost.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profileImage: true,
          },
        },
        _count: {
          select: {
            comments: true,
          },
        },
      },
      orderBy: { [query.sortBy]: query.sortOrder },
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    }),
    prisma.forumPost.count({ where }),
  ]);

  // Hide author info for anonymous posts
  const sanitizedPosts = posts.map(post => ({
    ...post,
    author: post.isAnonymous ? null : post.author,
    tags: post.tags ? JSON.parse(post.tags) : [],
  }));

  res.json({
    success: true,
    data: {
      posts: sanitizedPosts,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
        hasNext: query.page * query.limit < total,
        hasPrev: query.page > 1,
      },
    },
  });
}));

// Create forum post
router.post('/posts', requireEmailVerification, asyncHandler(async (req, res) => {
  const validatedData = createPostSchema.parse(req.body);
  const authorId = req.user!.id;

  const post = await prisma.forumPost.create({
    data: {
      authorId,
      title: validatedData.title,
      content: validatedData.content,
      category: validatedData.category,
      tags: JSON.stringify(validatedData.tags),
      isAnonymous: validatedData.isAnonymous,
    },
    include: {
      author: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          profileImage: true,
        },
      },
    },
  });

  // TODO: Implement AI analysis for crisis detection and content moderation

  res.status(201).json({
    success: true,
    message: 'Post created successfully',
    data: {
      ...post,
      author: post.isAnonymous ? null : post.author,
      tags: JSON.parse(post.tags || '[]'),
    },
  });
}));

// Get specific post with comments
router.get('/posts/:postId', asyncHandler(async (req, res) => {
  const { postId } = req.params;

  const post = await prisma.forumPost.findUnique({
    where: { id: postId },
    include: {
      author: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          profileImage: true,
        },
      },
      comments: {
        where: { status: 'ACTIVE' },
        include: {
          author: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              profileImage: true,
            },
          },
          replies: {
            where: { status: 'ACTIVE' },
            include: {
              author: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  profileImage: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'asc' },
      },
    },
  });

  if (!post || post.status !== 'ACTIVE') {
    throw new CustomError('Post not found', 404);
  }

  // Increment view count
  await prisma.forumPost.update({
    where: { id: postId },
    data: { viewCount: { increment: 1 } },
  });

  // Sanitize anonymous posts and comments
  const sanitizedPost = {
    ...post,
    author: post.isAnonymous ? null : post.author,
    tags: post.tags ? JSON.parse(post.tags) : [],
    comments: post.comments.map(comment => ({
      ...comment,
      author: comment.isAnonymous ? null : comment.author,
      replies: comment.replies.map(reply => ({
        ...reply,
        author: reply.isAnonymous ? null : reply.author,
      })),
    })),
  };

  res.json({
    success: true,
    data: sanitizedPost,
  });
}));

// Get forum categories
router.get('/categories', asyncHandler(async (req, res) => {
  const categories = await prisma.forumPost.groupBy({
    by: ['category'],
    _count: {
      category: true,
    },
    where: {
      status: 'ACTIVE',
    },
  });

  res.json({
    success: true,
    data: categories.map(cat => ({
      name: cat.category,
      postCount: cat._count.category,
    })),
  });
}));

export default router;
