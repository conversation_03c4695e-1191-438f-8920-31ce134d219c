
'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

export interface SpaceFiltersState {
  type: 'all' | 'free' | 'paid';
  tags: string[];
  sortBy: 'popularity' | 'newest' | 'name';
}

interface SpaceFiltersProps {
  allTags: string[];
  filters: SpaceFiltersState;
  onFilterChange: (newFilters: SpaceFiltersState) => void;
}

export function SpaceFilters({ allTags, filters, onFilterChange }: SpaceFiltersProps) {
  const handleTypeChange = (value: 'all' | 'free' | 'paid') => {
    onFilterChange({ ...filters, type: value });
  };

  const handleTagChange = (tag: string, checked: boolean) => {
    const newTags = checked
      ? [...filters.tags, tag]
      : filters.tags.filter(t => t !== tag);
    onFilterChange({ ...filters, tags: newTags });
  };

  const handleSortChange = (value: 'popularity' | 'newest' | 'name') => {
    onFilterChange({ ...filters, sortBy: value });
  };

  return (
    <Card className="shadow-md sticky top-20"> {/* sticky top to keep it visible on scroll */}
      <CardHeader>
        <CardTitle className="text-xl">Filter & Sort</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <Label className="font-semibold">Space Type</Label>
          <RadioGroup value={filters.type} onValueChange={handleTypeChange} className="mt-2 space-y-1">
            <div>
              <RadioGroupItem value="all" id="type-all" />
              <Label htmlFor="type-all" className="ml-2 font-normal">All</Label>
            </div>
            <div>
              <RadioGroupItem value="free" id="type-free" />
              <Label htmlFor="type-free" className="ml-2 font-normal">Free</Label>
            </div>
            <div>
              <RadioGroupItem value="paid" id="type-paid" />
              <Label htmlFor="type-paid" className="ml-2 font-normal">Paid</Label>
            </div>
          </RadioGroup>
        </div>

        <Separator />

        <div>
          <Label className="font-semibold">Tags</Label>
          <ScrollArea className="h-40 mt-2 pr-3">
            <div className="space-y-2">
              {allTags.map(tag => (
                <div key={tag} className="flex items-center">
                  <Checkbox
                    id={`tag-${tag}`}
                    checked={filters.tags.includes(tag)}
                    onCheckedChange={(checked) => handleTagChange(tag, !!checked)}
                  />
                  <Label htmlFor={`tag-${tag}`} className="ml-2 font-normal">
                    {tag}
                  </Label>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        <Separator />
        
        <div>
          <Label htmlFor="sort-by" className="font-semibold">Sort By</Label>
          <Select value={filters.sortBy} onValueChange={handleSortChange}>
            <SelectTrigger id="sort-by" className="mt-2">
              <SelectValue placeholder="Select sorting" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="popularity">Popularity</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="name">Name (A-Z)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
}
