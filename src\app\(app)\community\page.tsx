
'use client';

import React, { useState } from 'react';
import { ForumPostCard } from '@/components/community/forum-post-card';
import { NewPostForm } from '@/components/community/new-post-form';
import { PLACEHOLDER_FORUM_POSTS } from '@/lib/constants';
import type { ForumPost } from '@/lib/types';
import { Separator } from '@/components/ui/separator';
import { Users } from 'lucide-react';

export default function CommunityPage() {
  const [posts, setPosts] = useState<ForumPost[]>(PLACEHOLDER_FORUM_POSTS);

  const handleAddPost = (newPostData: Omit<ForumPost, 'id' | 'author' | 'date' | 'avatarUrl' | 'aiHint'>) => {
    const newPost: ForumPost = {
      ...newPostData,
      id: `fp${Date.now()}`,
      author: 'Anonymous', // Changed from 'CurrentUser'
      date: new Date().toISOString(),
      // avatarUrl and aiHint are optional and will result in AvatarFallback if not provided
    };
    setPosts(prevPosts => [newPost, ...prevPosts]);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-center mb-10">
        <Users className="h-10 w-10 text-primary mr-3" />
        <h1 className="text-3xl font-bold text-center text-primary">Community Forum</h1>
      </div>
       <p className="text-center text-muted-foreground mb-10 max-w-2xl mx-auto">
        Connect with others, share your experiences, and find support in our community.
      </p>

      <NewPostForm onAddPost={handleAddPost} />
      
      <Separator className="my-12" />

      <h2 className="text-2xl font-semibold mb-6 text-center">Recent Discussions</h2>
      {posts.length > 0 ? (
        <div className="space-y-6 max-w-2xl mx-auto">
          {posts.map((post) => (
            <ForumPostCard key={post.id} post={post} />
          ))}
        </div>
      ) : (
        <p className="text-center text-muted-foreground">No posts yet. Be the first to start a discussion!</p>
      )}
    </div>
  );
}
