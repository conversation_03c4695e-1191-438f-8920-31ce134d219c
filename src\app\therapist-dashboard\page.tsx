
'use client';

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { CalendarDays, Users, MessageSquare, Settings, BarChart3, FileText, Video, LogOut, PlusCircle, ExternalLink, Clock } from 'lucide-react';
import { Logo } from '@/components/icons/logo';
import { APP_NAME, PLACEHOLDER_THERAPIST_CLIENTS, PLACEHOLDER_THERAPIST_APPOINTMENTS } from '@/lib/constants';
import type { PlaceholderClient, PlaceholderAppointment } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';


export default function TherapistDashboardPage() {
  const { toast } = useToast();
  const router = useRouter();

  // Placeholder data - in a real app, this would come from an API
  const therapistName = "Dr. Emily Carter";
  const upcomingAppointmentsCount = 3;
  const activeClientsCount = 15;
  const unreadMessagesCount = 5;

  const today = new Date();
  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const currentDayName = daysOfWeek[today.getDay()];
  const currentMonthName = today.toLocaleString('default', { month: 'long' });


  const handleLogout = () => {
    toast({
      title: "Logged Out",
      description: "You have been successfully logged out.",
    });
    router.push('/'); // Redirect to homepage
  };

  const handlePlaceholderAction = (actionName: string, details?: string) => {
    toast({
      title: `Feature: ${actionName}`,
      description: details || `${actionName} functionality is a placeholder and will be implemented soon.`,
    });
  };

  return (
    <div className="flex flex-col min-h-screen bg-muted/40">
      <header className="sticky top-0 z-40 w-full border-b bg-background">
        <div className="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0 max-w-6xl mx-auto px-4">
          <Link href="/" className="flex items-center space-x-2">
            <Logo className="h-7 w-7 text-primary" />
            <span className="text-xl font-bold text-primary">{APP_NAME}</span>
          </Link>
          <div className="flex items-center space-x-4">
            <h1 className="hidden sm:block text-lg font-medium text-foreground">Therapist Dashboard</h1>
            <Button variant="outline" size="sm" onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" /> Logout
            </Button>
          </div>
        </div>
      </header>
      <main className="flex-1 p-4 md:p-6">
        <div className="container mx-auto max-w-6xl">
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-primary">Welcome back, {therapistName}!</h2>
            <p className="text-muted-foreground">Here's an overview of your therapist activities and tools.</p>
          </div>

          {/* Quick Stats */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
            <Card className="shadow-md">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Upcoming Appointments</CardTitle>
                <CalendarDays className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{upcomingAppointmentsCount}</div>
                <p className="text-xs text-muted-foreground">scheduled for this week</p>
              </CardContent>
            </Card>
            <Card className="shadow-md">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
                <Users className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{activeClientsCount}</div>
                <p className="text-xs text-muted-foreground">currently under your care</p>
              </CardContent>
            </Card>
            <Card className="shadow-md">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Unread Messages</CardTitle>
                <MessageSquare className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{unreadMessagesCount}</div>
                <p className="text-xs text-muted-foreground">from clients & admin</p>
              </CardContent>
            </Card>
          </div>

          {/* Main Dashboard Sections */}
          <div className="grid gap-8 lg:grid-cols-3">
            {/* Schedule Management - Enhanced */}
            <Card className="shadow-lg lg:col-span-2">
              <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-xl">
                    <CalendarDays className="mr-2 h-6 w-6 text-primary" />
                    Today's Schedule - {currentDayName}, {currentMonthName} {today.getDate()}
                    </CardTitle>
                    <Button variant="outline" size="sm" onClick={() => handlePlaceholderAction('Manage Availability')}>
                        <PlusCircle className="mr-2 h-4 w-4" /> Set Availability
                    </Button>
                </div>
                <CardDescription>Your upcoming appointments and daily agenda. Click an item for details.</CardDescription>
              </CardHeader>
              <CardContent>
                {PLACEHOLDER_THERAPIST_APPOINTMENTS.length > 0 ? (
                <ScrollArea className="h-[300px]">
                  <div className="space-y-3 pr-3">
                    {PLACEHOLDER_THERAPIST_APPOINTMENTS.map((apt) => (
                      <div key={apt.id} className="flex items-center p-3 bg-muted/50 rounded-lg hover:bg-muted transition-colors cursor-pointer" onClick={() => handlePlaceholderAction('View Appointment', `Details for ${apt.clientName}'s appointment at ${apt.time}`)}>
                        <Clock className="h-5 w-5 text-primary mr-3" />
                        <div className="flex-grow">
                          <p className="font-semibold">{apt.time} - {apt.clientName}</p>
                          <p className="text-xs text-muted-foreground">{apt.type} ({apt.duration})</p>
                        </div>
                        <Badge variant={apt.type === 'Initial Consultation' ? "destructive" : "secondary"}>{apt.type.split(' ')[0]}</Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
                ) : (
                    <p className="text-muted-foreground text-center py-8">No appointments scheduled for today.</p>
                )}
                 <Button variant="link" className="mt-4 px-0" onClick={() => handlePlaceholderAction('View Full Calendar')}>View Full Calendar &rarr;</Button>
              </CardContent>
            </Card>

            {/* Client Management - Enhanced */}
            <Card className="shadow-lg">
              <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-xl">
                    <Users className="mr-2 h-6 w-6 text-primary" />
                    Client Roster
                    </CardTitle>
                    <Button variant="outline" size="sm" onClick={() => handlePlaceholderAction('Add New Client')}>
                        <PlusCircle className="mr-2 h-4 w-4" /> Add Client
                    </Button>
                </div>
                <CardDescription>Access client profiles, notes, and communication history.</CardDescription>
              </CardHeader>
              <CardContent>
                 {PLACEHOLDER_THERAPIST_CLIENTS.length > 0 ? (
                 <ScrollArea className="h-[300px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {PLACEHOLDER_THERAPIST_CLIENTS.map((client) => (
                        <TableRow key={client.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                    <AvatarImage src={client.avatarUrl} alt={client.name} data-ai-hint={client.aiAvatarHint || "client photo"} />
                                    <AvatarFallback>{client.name.substring(0,1)}</AvatarFallback>
                                </Avatar>
                                <div>
                                    <p className="font-medium">{client.name}</p>
                                    <p className="text-xs text-muted-foreground">Last: {client.lastSession}</p>
                                </div>
                            </div>
                          </TableCell>
                          <TableCell><Badge variant={client.status === 'Active' ? 'default' : 'outline'}>{client.status}</Badge></TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handlePlaceholderAction('View Client Profile', `Viewing profile for ${client.name}`)}>
                                <ExternalLink className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  </ScrollArea>
                 ) : (
                    <p className="text-muted-foreground text-center py-8">No clients in your roster yet.</p>
                 )}
                 <Button variant="link" className="mt-4 px-0" onClick={() => handlePlaceholderAction('View All Clients')}>View All Clients &rarr;</Button>
              </CardContent>
            </Card>

            {/* Shared Resources */}
            <Card className="shadow-lg">
              <CardHeader>
                 <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-xl">
                    <FileText className="mr-2 h-6 w-6 text-primary" />
                    Shared Resources
                    </CardTitle>
                    <Button variant="outline" size="sm" onClick={() => handlePlaceholderAction('Upload Resource')}>
                        <PlusCircle className="mr-2 h-4 w-4" /> Upload
                    </Button>
                </div>
                <CardDescription>Manage and share resources with your clients or specific groups.</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">Resource management tools will appear here. (e.g., list of uploaded PDFs, videos)</p>
                <Button variant="outline" onClick={() => handlePlaceholderAction('Manage Resources Library')}>Manage My Resources</Button>
              </CardContent>
            </Card>

            {/* Hosted Spaces & Events */}
            <Card className="shadow-lg">
              <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-xl">
                        <Video className="mr-2 h-6 w-6 text-primary" />
                        My WELL Spaces
                    </CardTitle>
                     <Button variant="outline" size="sm" onClick={() => handlePlaceholderAction("Create New Space")}>
                        <PlusCircle className="mr-2 h-4 w-4" /> Create Space
                    </Button>
                </div>
                <CardDescription>Manage your WELL People Hub spaces and live events.</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">Tools for managing your community spaces and events will be here.</p>
                <Button variant="outline" onClick={() => handlePlaceholderAction("Therapist Space Management")}>
                    Go to My Space Settings
                </Button>
              </CardContent>
            </Card>

            {/* Account Settings */}
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Settings className="mr-2 h-6 w-6 text-primary" />
                  Account & Profile
                </CardTitle>
                <CardDescription>Update your public profile, payment information, and notification preferences.</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">Your account settings and preferences will be managed here.</p>
                <Button variant="outline" onClick={() => handlePlaceholderAction('Edit My Therapist Profile')}>Edit My Public Profile</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <footer className="py-6 md:px-8 md:py-0 border-t bg-background">
        <div className="container flex flex-col items-center justify-center gap-4 md:h-20 md:flex-row max-w-6xl mx-auto">
          <p className="text-balance text-center text-sm leading-loose text-muted-foreground md:text-left">
            &copy; {new Date().getFullYear()} {APP_NAME} Therapist Portal. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
