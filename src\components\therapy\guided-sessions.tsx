
'use client';

import React from 'react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PLACEHOLDER_GUIDED_SESSIONS } from '@/lib/constants';
import type { GuidedSession } from '@/lib/types';
import { PlayCircle, Headphones, Video, Clock, ListFilter } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"


function SessionCard({ session }: { session: GuidedSession }) {
  const { toast } = useToast();

  const handlePlaySession = () => {
    toast({
      title: "Playing Session (Simulated)",
      description: `"${session.title}" would start playing now. (Link: ${session.sourceUrl})`,
    });
    // In a real app, you might open a modal with an audio/video player or navigate
  };

  return (
    <Card className="flex flex-col h-full shadow-md hover:shadow-lg transition-shadow">
      <CardHeader className="p-0">
        <div className="relative aspect-video w-full rounded-t-md overflow-hidden">
          <Image
            src={session.thumbnailUrl}
            alt={session.title}
            layout="fill"
            objectFit="cover"
            data-ai-hint={session.aiHint || "session image"}
          />
           <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
           <Badge variant="secondary" className="absolute top-2 right-2 capitalize flex items-center gap-1">
            {session.type === 'audio' ? <Headphones className="h-3 w-3" /> : <Video className="h-3 w-3" />}
            {session.type}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="flex-grow pt-4">
        <CardTitle className="text-lg mb-1 line-clamp-2">{session.title}</CardTitle>
        <div className="flex items-center text-xs text-muted-foreground mb-2">
            <Badge variant="outline" className="mr-2 capitalize">{session.category}</Badge>
            <Clock className="h-3 w-3 mr-1" /> {session.duration}
        </div>
        <CardDescription className="text-sm line-clamp-3">{session.description}</CardDescription>
      </CardContent>
      <CardFooter>
        <Button className="w-full" onClick={handlePlaySession}>
          <PlayCircle className="mr-2 h-5 w-5" /> Play Session
        </Button>
      </CardFooter>
    </Card>
  );
}


export function GuidedSessions() {
  const [filter, setFilter] = React.useState("all")

  const filteredSessions = PLACEHOLDER_GUIDED_SESSIONS.filter(session => {
    if (filter === "all") return true;
    return session.category.toLowerCase() === filter.toLowerCase() || session.type.toLowerCase() === filter.toLowerCase();
  });

  const categories = ["all", ...Array.from(new Set(PLACEHOLDER_GUIDED_SESSIONS.map(s => s.category)))];
  const types = ["audio", "video"];


  return (
    <div className="space-y-8">
       <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl flex items-center gap-2"><Headphones className="h-7 w-7 text-primary"/>Guided Audio & Video Sessions</CardTitle>
          <CardDescription>Explore a library of sessions for anxiety, stress, sleep, and more. Click play to simulate session start.</CardDescription>
        </CardHeader>
        <CardContent>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="ml-auto">
                        <ListFilter className="mr-2 h-4 w-4" /> Filter ({filter === "all" ? "All" : filter.charAt(0).toUpperCase() + filter.slice(1)})
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                    <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuRadioGroup value={filter} onValueChange={setFilter}>
                    {categories.map(cat => (
                        <DropdownMenuRadioItem key={cat} value={cat} className="capitalize">{cat}</DropdownMenuRadioItem>
                    ))}
                    </DropdownMenuRadioGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
                     <DropdownMenuRadioGroup value={filter} onValueChange={setFilter}>
                        {types.map(type => (
                            <DropdownMenuRadioItem key={type} value={type} className="capitalize">{type}</DropdownMenuRadioItem>
                        ))}
                    </DropdownMenuRadioGroup>
                </DropdownMenuContent>
            </DropdownMenu>
        </CardContent>
      </Card>

      {filteredSessions.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSessions.map((session) => (
            <SessionCard key={session.id} session={session} />
          ))}
        </div>
      ) : (
         <p className="text-center text-muted-foreground py-10">No sessions match your filter criteria.</p>
      )}
    </div>
  );
}
