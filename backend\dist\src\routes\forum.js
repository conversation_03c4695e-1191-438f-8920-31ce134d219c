"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const zod_1 = require("zod");
const server_1 = require("../../server");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const createPostSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Title is required').max(200, 'Title too long'),
    content: zod_1.z.string().min(1, 'Content is required').max(10000, 'Content too long'),
    category: zod_1.z.string().min(1, 'Category is required'),
    tags: zod_1.z.array(zod_1.z.string()).optional().default([]),
    isAnonymous: zod_1.z.boolean().optional().default(false),
});
const postQuerySchema = zod_1.z.object({
    page: zod_1.z.string().transform(Number).optional().default(1),
    limit: zod_1.z.string().transform(Number).optional().default(20),
    category: zod_1.z.string().optional(),
    search: zod_1.z.string().optional(),
    sortBy: zod_1.z.enum(['createdAt', 'viewCount', 'likeCount']).optional().default('createdAt'),
    sortOrder: zod_1.z.enum(['asc', 'desc']).optional().default('desc'),
});
router.get('/posts', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const query = postQuerySchema.parse(req.query);
    const where = {
        status: 'ACTIVE',
    };
    if (query.category) {
        where.category = query.category;
    }
    if (query.search) {
        where.OR = [
            { title: { contains: query.search } },
            { content: { contains: query.search } },
        ];
    }
    const [posts, total] = await Promise.all([
        server_1.prisma.forumPost.findMany({
            where,
            include: {
                author: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        profileImage: true,
                    },
                },
                _count: {
                    select: {
                        comments: true,
                    },
                },
            },
            orderBy: { [query.sortBy]: query.sortOrder },
            skip: (query.page - 1) * query.limit,
            take: query.limit,
        }),
        server_1.prisma.forumPost.count({ where }),
    ]);
    const sanitizedPosts = posts.map(post => ({
        ...post,
        author: post.isAnonymous ? null : post.author,
        tags: post.tags ? JSON.parse(post.tags) : [],
    }));
    res.json({
        success: true,
        data: {
            posts: sanitizedPosts,
            pagination: {
                page: query.page,
                limit: query.limit,
                total,
                totalPages: Math.ceil(total / query.limit),
                hasNext: query.page * query.limit < total,
                hasPrev: query.page > 1,
            },
        },
    });
}));
router.post('/posts', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = createPostSchema.parse(req.body);
    const authorId = req.user.id;
    const post = await server_1.prisma.forumPost.create({
        data: {
            authorId,
            title: validatedData.title,
            content: validatedData.content,
            category: validatedData.category,
            tags: JSON.stringify(validatedData.tags),
            isAnonymous: validatedData.isAnonymous,
        },
        include: {
            author: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    profileImage: true,
                },
            },
        },
    });
    res.status(201).json({
        success: true,
        message: 'Post created successfully',
        data: {
            ...post,
            author: post.isAnonymous ? null : post.author,
            tags: JSON.parse(post.tags || '[]'),
        },
    });
}));
router.get('/posts/:postId', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { postId } = req.params;
    const post = await server_1.prisma.forumPost.findUnique({
        where: { id: postId },
        include: {
            author: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    profileImage: true,
                },
            },
            comments: {
                where: { status: 'ACTIVE' },
                include: {
                    author: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            profileImage: true,
                        },
                    },
                    replies: {
                        where: { status: 'ACTIVE' },
                        include: {
                            author: {
                                select: {
                                    id: true,
                                    firstName: true,
                                    lastName: true,
                                    profileImage: true,
                                },
                            },
                        },
                    },
                },
                orderBy: { createdAt: 'asc' },
            },
        },
    });
    if (!post || post.status !== 'ACTIVE') {
        throw new errorHandler_1.CustomError('Post not found', 404);
    }
    await server_1.prisma.forumPost.update({
        where: { id: postId },
        data: { viewCount: { increment: 1 } },
    });
    const sanitizedPost = {
        ...post,
        author: post.isAnonymous ? null : post.author,
        tags: post.tags ? JSON.parse(post.tags) : [],
        comments: post.comments.map(comment => ({
            ...comment,
            author: comment.isAnonymous ? null : comment.author,
            replies: comment.replies.map(reply => ({
                ...reply,
                author: reply.isAnonymous ? null : reply.author,
            })),
        })),
    };
    res.json({
        success: true,
        data: sanitizedPost,
    });
}));
router.get('/categories', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const categories = await server_1.prisma.forumPost.groupBy({
        by: ['category'],
        _count: {
            category: true,
        },
        where: {
            status: 'ACTIVE',
        },
    });
    res.json({
        success: true,
        data: categories.map(cat => ({
            name: cat.category,
            postCount: cat._count.category,
        })),
    });
}));
exports.default = router;
//# sourceMappingURL=forum.js.map