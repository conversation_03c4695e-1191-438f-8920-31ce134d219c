import { Router } from 'express';
import { z } from 'zod';
import { UserRole } from '@prisma/client';
import { prisma } from '../../server';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { requireRole, requireEmailVerification } from '../middleware/auth';
import { AuthUtils } from '../utils/auth';

const router = Router();

// Validation schemas
const updateProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phoneNumber: z.string().optional(),
  dateOfBirth: z.string().optional(),
  timezone: z.string().optional(),
  profileImage: z.string().url().optional(),
});

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
});

const setupTwoFactorSchema = z.object({
  enable: z.boolean(),
  token: z.string().optional(),
});

// Get user profile
router.get('/profile', requireEmailVerification, asyncHandler(async (req, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      emailVerified: true,
      twoFactorEnabled: true,
      profileImage: true,
      phoneNumber: true,
      dateOfBirth: true,
      timezone: true,
      createdAt: true,
      lastLoginAt: true,
      patientProfile: {
        select: {
          emergencyContact: true,
          medicalHistory: true,
          currentMedications: true,
          allergies: true,
          preferredLanguages: true,
          consentToAI: true,
        },
      },
      psychiatristProfile: {
        select: {
          licenseNumber: true,
          specialties: true,
          bio: true,
          education: true,
          experience: true,
          languages: true,
          hourlyRate: true,
          acceptsInsurance: true,
          verified: true,
        },
      },
    },
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Parse JSON fields
  const userData = {
    ...user,
    psychiatristProfile: user.psychiatristProfile ? {
      ...user.psychiatristProfile,
      specialties: user.psychiatristProfile.specialties ? JSON.parse(user.psychiatristProfile.specialties) : [],
      education: user.psychiatristProfile.education ? JSON.parse(user.psychiatristProfile.education) : [],
      languages: user.psychiatristProfile.languages ? JSON.parse(user.psychiatristProfile.languages) : [],
    } : null,
  };

  res.json({
    success: true,
    data: userData,
  });
}));

// Update user profile
router.put('/profile', requireEmailVerification, asyncHandler(async (req, res) => {
  const validatedData = updateProfileSchema.parse(req.body);
  
  const updateData: any = {};
  
  if (validatedData.firstName) updateData.firstName = validatedData.firstName;
  if (validatedData.lastName) updateData.lastName = validatedData.lastName;
  if (validatedData.phoneNumber) updateData.phoneNumber = validatedData.phoneNumber;
  if (validatedData.timezone) updateData.timezone = validatedData.timezone;
  if (validatedData.profileImage) updateData.profileImage = validatedData.profileImage;
  if (validatedData.dateOfBirth) updateData.dateOfBirth = new Date(validatedData.dateOfBirth);

  const user = await prisma.user.update({
    where: { id: req.user!.id },
    data: updateData,
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      phoneNumber: true,
      dateOfBirth: true,
      timezone: true,
      profileImage: true,
      updatedAt: true,
    },
  });

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: user,
  });
}));

// Change password
router.put('/password', requireEmailVerification, asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = changePasswordSchema.parse(req.body);

  // Validate new password strength
  const passwordValidation = AuthUtils.validatePassword(newPassword);
  if (!passwordValidation.isValid) {
    throw new CustomError(passwordValidation.errors.join(', '), 400);
  }

  // Get current user with password
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: { id: true, password: true },
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Verify current password
  const isCurrentPasswordValid = await AuthUtils.comparePassword(currentPassword, user.password);
  if (!isCurrentPasswordValid) {
    throw new CustomError('Current password is incorrect', 400);
  }

  // Hash new password
  const hashedNewPassword = await AuthUtils.hashPassword(newPassword);

  // Update password
  await prisma.user.update({
    where: { id: req.user!.id },
    data: { password: hashedNewPassword },
  });

  res.json({
    success: true,
    message: 'Password changed successfully',
  });
}));

// Setup/disable two-factor authentication
router.post('/two-factor', requireEmailVerification, asyncHandler(async (req, res) => {
  const { enable, token } = setupTwoFactorSchema.parse(req.body);

  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: { id: true, twoFactorEnabled: true, twoFactorSecret: true },
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  if (enable) {
    if (user.twoFactorEnabled) {
      throw new CustomError('Two-factor authentication is already enabled', 400);
    }

    // Generate new secret if not exists
    let secret = user.twoFactorSecret;
    if (!secret) {
      const twoFactorData = AuthUtils.generateTwoFactorSecret();
      secret = twoFactorData.secret;
      
      await prisma.user.update({
        where: { id: req.user!.id },
        data: { twoFactorSecret: secret },
      });

      return res.json({
        success: true,
        message: 'Two-factor authentication secret generated',
        data: {
          secret,
          qrCodeUrl: twoFactorData.qrCodeUrl,
        },
      });
    }

    // Verify token to enable 2FA
    if (!token) {
      throw new CustomError('Token is required to enable two-factor authentication', 400);
    }

    const isValidToken = AuthUtils.verifyTwoFactorToken(token, secret);
    if (!isValidToken) {
      throw new CustomError('Invalid two-factor authentication token', 400);
    }

    await prisma.user.update({
      where: { id: req.user!.id },
      data: { twoFactorEnabled: true },
    });

    res.json({
      success: true,
      message: 'Two-factor authentication enabled successfully',
    });
  } else {
    if (!user.twoFactorEnabled) {
      throw new CustomError('Two-factor authentication is not enabled', 400);
    }

    // Verify token to disable 2FA
    if (!token || !user.twoFactorSecret) {
      throw new CustomError('Token is required to disable two-factor authentication', 400);
    }

    const isValidToken = AuthUtils.verifyTwoFactorToken(token, user.twoFactorSecret);
    if (!isValidToken) {
      throw new CustomError('Invalid two-factor authentication token', 400);
    }

    await prisma.user.update({
      where: { id: req.user!.id },
      data: {
        twoFactorEnabled: false,
        twoFactorSecret: null,
      },
    });

    res.json({
      success: true,
      message: 'Two-factor authentication disabled successfully',
    });
  }
}));

// Get user statistics (for dashboard)
router.get('/stats', requireEmailVerification, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const userRole = req.user!.role;

  let stats: any = {};

  if (userRole === UserRole.PATIENT) {
    const [totalSessions, completedSessions, upcomingSessions, totalMessages] = await Promise.all([
      prisma.session.count({ where: { patientId: userId } }),
      prisma.session.count({ where: { patientId: userId, status: 'COMPLETED' } }),
      prisma.session.count({ 
        where: { 
          patientId: userId, 
          status: 'SCHEDULED',
          scheduledAt: { gt: new Date() }
        } 
      }),
      prisma.message.count({ where: { senderId: userId } }),
    ]);

    stats = {
      totalSessions,
      completedSessions,
      upcomingSessions,
      totalMessages,
    };
  } else if (userRole === UserRole.PSYCHIATRIST) {
    const [totalSessions, completedSessions, upcomingSessions, totalPatients, totalEarnings] = await Promise.all([
      prisma.session.count({ where: { psychiatristId: userId } }),
      prisma.session.count({ where: { psychiatristId: userId, status: 'COMPLETED' } }),
      prisma.session.count({ 
        where: { 
          psychiatristId: userId, 
          status: 'SCHEDULED',
          scheduledAt: { gt: new Date() }
        } 
      }),
      prisma.session.groupBy({
        by: ['patientId'],
        where: { psychiatristId: userId },
        _count: { patientId: true },
      }).then(result => result.length),
      prisma.payment.aggregate({
        where: {
          session: { psychiatristId: userId },
          status: 'COMPLETED',
        },
        _sum: { amount: true },
      }).then(result => result._sum.amount || 0),
    ]);

    stats = {
      totalSessions,
      completedSessions,
      upcomingSessions,
      totalPatients,
      totalEarnings,
    };
  }

  res.json({
    success: true,
    data: stats,
  });
}));

// Delete user account
router.delete('/account', requireEmailVerification, asyncHandler(async (req, res) => {
  const userId = req.user!.id;

  // Check for active sessions
  const activeSessions = await prisma.session.count({
    where: {
      OR: [
        { patientId: userId },
        { psychiatristId: userId },
      ],
      status: { in: ['SCHEDULED', 'IN_PROGRESS'] },
    },
  });

  if (activeSessions > 0) {
    throw new CustomError('Cannot delete account with active sessions. Please complete or cancel all sessions first.', 400);
  }

  // Soft delete by updating status
  await prisma.user.update({
    where: { id: userId },
    data: {
      status: 'BANNED',
      email: `deleted_${Date.now()}_${req.user!.email}`,
    },
  });

  res.json({
    success: true,
    message: 'Account deleted successfully',
  });
}));

export default router;
