"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
const server_1 = require("../../server");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const auth_2 = require("../utils/auth");
const router = (0, express_1.Router)();
const createSessionSchema = zod_1.z.object({
    psychiatristId: zod_1.z.string().cuid('Invalid psychiatrist ID'),
    scheduledAt: zod_1.z.string().datetime('Invalid date format'),
    duration: zod_1.z.number().min(15).max(180).optional().default(60),
    notes: zod_1.z.string().optional(),
});
const updateSessionSchema = zod_1.z.object({
    status: zod_1.z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW']).optional(),
    sessionNotes: zod_1.z.string().optional(),
    patientNotes: zod_1.z.string().optional(),
});
const sessionQuerySchema = zod_1.z.object({
    page: zod_1.z.string().transform(Number).optional().default(1),
    limit: zod_1.z.string().transform(Number).optional().default(10),
    status: zod_1.z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW']).optional(),
    startDate: zod_1.z.string().datetime().optional(),
    endDate: zod_1.z.string().datetime().optional(),
});
router.post('/', auth_1.requireEmailVerification, (0, auth_1.requireRole)([client_1.UserRole.PATIENT]), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = createSessionSchema.parse(req.body);
    const patientId = req.user.id;
    const psychiatrist = await server_1.prisma.user.findUnique({
        where: { id: validatedData.psychiatristId },
        include: { psychiatristProfile: true },
    });
    if (!psychiatrist || psychiatrist.role !== client_1.UserRole.PSYCHIATRIST) {
        throw new errorHandler_1.CustomError('Psychiatrist not found', 404);
    }
    if (!psychiatrist.psychiatristProfile?.verified) {
        throw new errorHandler_1.CustomError('Psychiatrist is not verified', 400);
    }
    if (psychiatrist.status !== 'ACTIVE') {
        throw new errorHandler_1.CustomError('Psychiatrist is not available', 400);
    }
    const scheduledAt = new Date(validatedData.scheduledAt);
    const endTime = new Date(scheduledAt.getTime() + validatedData.duration * 60000);
    const conflictingSessions = await server_1.prisma.session.count({
        where: {
            psychiatristId: validatedData.psychiatristId,
            status: { in: ['SCHEDULED', 'IN_PROGRESS'] },
            OR: [
                {
                    scheduledAt: { lte: scheduledAt },
                    AND: {
                        scheduledAt: {
                            gte: new Date(scheduledAt.getTime() - validatedData.duration * 60000)
                        }
                    }
                },
                {
                    scheduledAt: { gte: scheduledAt, lte: endTime }
                }
            ]
        }
    });
    if (conflictingSessions > 0) {
        throw new errorHandler_1.CustomError('Time slot is not available', 409);
    }
    const jitsiRoomId = auth_2.AuthUtils.generateJitsiRoomId(auth_2.AuthUtils.generateSessionId());
    const session = await server_1.prisma.session.create({
        data: {
            patientId,
            psychiatristId: validatedData.psychiatristId,
            scheduledAt,
            duration: validatedData.duration,
            jitsiRoomId,
            patientNotes: validatedData.notes,
            status: client_1.SessionStatus.SCHEDULED,
        },
        include: {
            patient: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                },
            },
            psychiatrist: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    psychiatristProfile: {
                        select: {
                            hourlyRate: true,
                            specialties: true,
                        },
                    },
                },
            },
        },
    });
    res.status(201).json({
        success: true,
        message: 'Session created successfully',
        data: session,
    });
}));
router.get('/', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const query = sessionQuerySchema.parse(req.query);
    const userId = req.user.id;
    const userRole = req.user.role;
    const where = {};
    if (userRole === client_1.UserRole.PATIENT) {
        where.patientId = userId;
    }
    else if (userRole === client_1.UserRole.PSYCHIATRIST) {
        where.psychiatristId = userId;
    }
    else {
    }
    if (query.status) {
        where.status = query.status;
    }
    if (query.startDate || query.endDate) {
        where.scheduledAt = {};
        if (query.startDate) {
            where.scheduledAt.gte = new Date(query.startDate);
        }
        if (query.endDate) {
            where.scheduledAt.lte = new Date(query.endDate);
        }
    }
    const [sessions, total] = await Promise.all([
        server_1.prisma.session.findMany({
            where,
            include: {
                patient: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                psychiatrist: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        psychiatristProfile: {
                            select: {
                                hourlyRate: true,
                                specialties: true,
                            },
                        },
                    },
                },
                payment: {
                    select: {
                        id: true,
                        amount: true,
                        status: true,
                    },
                },
            },
            orderBy: { scheduledAt: 'desc' },
            skip: (query.page - 1) * query.limit,
            take: query.limit,
        }),
        server_1.prisma.session.count({ where }),
    ]);
    res.json({
        success: true,
        data: {
            sessions,
            pagination: {
                page: query.page,
                limit: query.limit,
                total,
                totalPages: Math.ceil(total / query.limit),
                hasNext: query.page * query.limit < total,
                hasPrev: query.page > 1,
            },
        },
    });
}));
router.get('/:sessionId', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { sessionId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;
    const session = await server_1.prisma.session.findUnique({
        where: { id: sessionId },
        include: {
            patient: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                },
            },
            psychiatrist: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    psychiatristProfile: {
                        select: {
                            hourlyRate: true,
                            specialties: true,
                            bio: true,
                        },
                    },
                },
            },
            payment: {
                select: {
                    id: true,
                    amount: true,
                    status: true,
                    stripePaymentIntentId: true,
                },
            },
            messages: {
                include: {
                    sender: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                },
                orderBy: { createdAt: 'asc' },
            },
        },
    });
    if (!session) {
        throw new errorHandler_1.CustomError('Session not found', 404);
    }
    const canAccess = userRole === client_1.UserRole.ADMIN ||
        userRole === client_1.UserRole.MODERATOR ||
        session.patientId === userId ||
        session.psychiatristId === userId;
    if (!canAccess) {
        throw new errorHandler_1.CustomError('Access denied', 403);
    }
    res.json({
        success: true,
        data: session,
    });
}));
router.put('/:sessionId', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { sessionId } = req.params;
    const validatedData = updateSessionSchema.parse(req.body);
    const userId = req.user.id;
    const userRole = req.user.role;
    const session = await server_1.prisma.session.findUnique({
        where: { id: sessionId },
    });
    if (!session) {
        throw new errorHandler_1.CustomError('Session not found', 404);
    }
    const canUpdate = userRole === client_1.UserRole.ADMIN ||
        session.patientId === userId ||
        session.psychiatristId === userId;
    if (!canUpdate) {
        throw new errorHandler_1.CustomError('Access denied', 403);
    }
    if (validatedData.status) {
        const validTransitions = {
            SCHEDULED: ['IN_PROGRESS', 'CANCELLED', 'NO_SHOW'],
            IN_PROGRESS: ['COMPLETED', 'CANCELLED'],
            COMPLETED: [],
            CANCELLED: [],
            NO_SHOW: [],
        };
        if (!validTransitions[session.status].includes(validatedData.status)) {
            throw new errorHandler_1.CustomError(`Cannot change status from ${session.status} to ${validatedData.status}`, 400);
        }
    }
    const updateData = {};
    if (validatedData.status) {
        updateData.status = validatedData.status;
        if (validatedData.status === client_1.SessionStatus.IN_PROGRESS && !session.startedAt) {
            updateData.startedAt = new Date();
        }
        if (validatedData.status === client_1.SessionStatus.COMPLETED && !session.endedAt) {
            updateData.endedAt = new Date();
        }
    }
    if (validatedData.sessionNotes && session.psychiatristId === userId) {
        updateData.sessionNotes = validatedData.sessionNotes;
    }
    if (validatedData.patientNotes && session.patientId === userId) {
        updateData.patientNotes = validatedData.patientNotes;
    }
    const updatedSession = await server_1.prisma.session.update({
        where: { id: sessionId },
        data: updateData,
        include: {
            patient: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                },
            },
            psychiatrist: {
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                },
            },
        },
    });
    res.json({
        success: true,
        message: 'Session updated successfully',
        data: updatedSession,
    });
}));
router.delete('/:sessionId', auth_1.requireEmailVerification, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { sessionId } = req.params;
    const userId = req.user.id;
    const session = await server_1.prisma.session.findUnique({
        where: { id: sessionId },
        include: { payment: true },
    });
    if (!session) {
        throw new errorHandler_1.CustomError('Session not found', 404);
    }
    if (session.patientId !== userId && session.psychiatristId !== userId) {
        throw new errorHandler_1.CustomError('Access denied', 403);
    }
    if (session.status !== client_1.SessionStatus.SCHEDULED) {
        throw new errorHandler_1.CustomError('Can only cancel scheduled sessions', 400);
    }
    const hoursUntilSession = (session.scheduledAt.getTime() - Date.now()) / (1000 * 60 * 60);
    if (hoursUntilSession < 24) {
        throw new errorHandler_1.CustomError('Cannot cancel session less than 24 hours before scheduled time', 400);
    }
    await server_1.prisma.session.update({
        where: { id: sessionId },
        data: { status: client_1.SessionStatus.CANCELLED },
    });
    res.json({
        success: true,
        message: 'Session cancelled successfully',
    });
}));
exports.default = router;
//# sourceMappingURL=sessions.js.map