
# WELL - Your Mental Wellness Companion

This is a Next.js web application designed as a personal companion for mental wellness, offering AI-driven insights, a supportive community, and guided journaling.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Getting Started](#getting-started)
  - [1. <PERSON><PERSON> the Repository](#1-clone-the-repository)
  - [2. Install Dependencies](#2-install-dependencies)
  - [3. Environment Variables](#3-environment-variables)
  - [4. Running the Application](#4-running-the-application)
    - [Next.js Frontend](#nextjs-frontend)
    - [Genkit AI Flows](#genkit-ai-flows)
- [Available Scripts](#available-scripts)
- [Project Structure](#project-structure)
- [Tech Stack](#tech-stack)

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js**: Version 18.x or later (v20.x recommended). You can download it from [nodejs.org](https://nodejs.org/).
- **npm** (Node Package Manager) or **yarn**: These come bundled with Node.js. This project uses `npm` by default as per `package.json` scripts.

## Getting Started

Follow these steps to set up and run the WELL application locally.

### 1. Clone the Repository

If you haven't already, clone the project repository to your local machine:

```bash
git clone <your-repository-url>
cd <project-directory-name>
```

Replace `<your-repository-url>` with the actual URL of your Git repository and `<project-directory-name>` with the name of the project folder.

### 2. Install Dependencies

Navigate to the project's root directory (if you're not already there) and install the necessary dependencies:

Using npm:
```bash
npm install
```

Or, if you prefer using yarn:
```bash
yarn install
```

This will download and install all the packages listed in `package.json`.

### 3. Environment Variables

This project uses a `.env` file for environment-specific configurations, particularly for Genkit AI services.

1.  **Create a `.env` file**: In the root directory of the project, create a file named `.env`.
2.  **Populate `.env`**:
    You will need to add your Google AI API key (for Gemini models used by Genkit) to this file.
    ```env
    GOOGLE_API_KEY=your_google_ai_api_key_here
    ```
    Replace `your_google_ai_api_key_here` with your actual API key. You can obtain one from [Google AI Studio](https://aistudio.google.com/app/apikey).

    *Note: The `.env` file is listed in `.gitignore` to prevent accidental commits of sensitive keys.*

### 4. Running the Application

The WELL application consists of two main parts that need to be run separately: the Next.js frontend and the Genkit AI flows server.

#### Next.js Frontend

This server handles the user interface and most of the application logic.

To start the Next.js development server:
```bash
npm run dev
```

This command usually starts the application on [http://localhost:9002](http://localhost:9002) (as specified in the `dev` script in `package.json`). Open this URL in your web browser to see the application. The server will automatically reload if you make changes to the frontend code.

#### Genkit AI Flows

This server handles the AI-powered features like journal insights and personalized recommendations. The Next.js application will make requests to this server.

To start the Genkit development server:
```bash
npm run genkit:dev
```
Or, to have it watch for changes and restart automatically:
```bash
npm run genkit:watch
```

The Genkit server typically starts on [http://localhost:3400](http://localhost:3400) by default, and its developer UI (Flows UI) is accessible at [http://localhost:4000](http://localhost:4000). You don't need to access these URLs directly when using the WELL app, but the Flows UI can be useful for debugging your AI flows.

**Both servers (Next.js and Genkit) need to be running simultaneously for all features of the WELL application to work correctly.**

## Available Scripts

In the `package.json` file, you will find several scripts for managing the application:

-   `npm run dev`: Starts the Next.js development server (with Turbopack).
-   `npm run genkit:dev`: Starts the Genkit development server.
-   `npm run genkit:watch`: Starts the Genkit development server with file watching.
-   `npm run build`: Builds the Next.js application for production.
-   `npm run start`: Starts a Next.js production server (after running `build`).
-   `npm run lint`: Lints the codebase using Next.js's built-in ESLint configuration.
-   `npm run typecheck`: Runs TypeScript to check for type errors.

## Project Structure

A brief overview of the key directories:

-   `src/app/`: Contains the main application pages and layouts (using Next.js App Router).
    -   `src/app/(app)/`: User-facing authenticated routes.
    -   `src/app/(therapist)/`: Therapist-facing routes (currently simulated).
    -   `src/app/api/`: API routes (if any).
-   `src/components/`: Shared React components.
    -   `src/components/ui/`: ShadCN UI components.
    -   `src/components/landing/`: Components for the landing page.
    -   `src/components/journal/`: Components for the journaling feature.
    -   ...and so on for other features.
-   `src/ai/`: Contains Genkit AI flows and configuration.
    -   `src/ai/flows/`: Specific AI flow implementations.
-   `src/lib/`: Utility functions, constants, type definitions.
-   `src/contexts/`: React Context providers.
-   `src/hooks/`: Custom React hooks.
-   `public/`: Static assets.

## Tech Stack

-   **Framework**: Next.js (with App Router)
-   **Language**: TypeScript
-   **Styling**: Tailwind CSS
-   **UI Components**: ShadCN UI
-   **AI Integration**: Genkit (with Google AI/Gemini)
-   **State Management**: React Context (for `JournalProvider`)
-   **Forms**: React Hook Form with Zod for validation

Happy coding, and we hope WELL helps you on your journey to better mental wellness!
