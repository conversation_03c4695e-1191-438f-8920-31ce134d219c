
'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { CalendarClock, Video, Users, Ticket } from 'lucide-react';
import type { SpaceEvent } from '@/lib/types'; // Assuming SpaceEvent type exists
import { Badge } from '@/components/ui/badge';

// Placeholder events data
const placeholderEvents: SpaceEvent[] = [
  {
    id: 'evt1',
    spaceId: 'space1',
    title: 'Weekly Mindfulness Check-in',
    description: 'Join us for a guided mindfulness session and group discussion.',
    dateTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
    durationMinutes: 60,
    platform: 'Jitsi',
    rsvps: 25,
    maxAttendees: 50,
    meetingLink: '#'
  },
  {
    id: 'evt2',
    spaceId: 'space1',
    title: 'Expert Q&A: Managing Holiday Stress',
    description: 'Live Q&A with <PERSON><PERSON> <PERSON> on coping with holiday-related stress.',
    dateTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
    durationMinutes: 90,
    platform: 'Zoom',
    rsvps: 40,
    meetingLink: '#'
  },
];

export function LiveEventsSection({ spaceId }: { spaceId: string }) {
  // In a real app, fetch events for the given spaceId
  const events = placeholderEvents.filter(event => event.spaceId === spaceId || placeholderEvents); // Show all for now

  return (
    <Card className="shadow-md">
      <CardHeader>
        <CardTitle className="flex items-center text-2xl">
          <CalendarClock className="mr-2 h-6 w-6 text-primary" />
          Live Events & Webinars
        </CardTitle>
        <CardDescription>
          Join live sessions, workshops, and Q&As hosted in this space. (Jitsi/Zoom integration placeholders)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {events.length > 0 ? (
          events.map(event => (
            <Card key={event.id} className="bg-muted/50">
              <CardHeader>
                <CardTitle className="text-lg">{event.title}</CardTitle>
                <div className="text-xs text-muted-foreground flex flex-wrap gap-x-3 gap-y-1">
                  <span>{new Date(event.dateTime).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</span>
                  <span>{new Date(event.dateTime).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}</span>
                  <span>({event.durationMinutes} min)</span>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-foreground/80 mb-2">{event.description}</p>
                 <Badge variant="outline" className="capitalize">
                    <Video className="mr-1 h-3 w-3" /> {event.platform}
                </Badge>
              </CardContent>
              <CardFooter className="flex justify-between items-center">
                <div className="text-sm text-muted-foreground flex items-center">
                    <Users className="mr-1 h-4 w-4" /> {event.rsvps} {event.maxAttendees ? `/ ${event.maxAttendees}`: ''} attending
                </div>
                <Button size="sm" variant="default">
                    <Ticket className="mr-2 h-4 w-4" /> RSVP / Join
                </Button>
              </CardFooter>
            </Card>
          ))
        ) : (
          <p className="text-muted-foreground text-center py-4">No upcoming live events scheduled.</p>
        )}
      </CardContent>
       <CardFooter>
        <p className="text-xs text-muted-foreground">
          Event functionality is a placeholder. Integrations with platforms like Jitsi would provide video conferencing.
        </p>
      </CardFooter>
    </Card>
  );
}
