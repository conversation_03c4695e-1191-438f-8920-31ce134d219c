import { ResourceCard } from '@/components/resources/resource-card';
import { PLACEHOLDER_RESOURCES } from '@/lib/constants';
import { Library } from 'lucide-react';

export default function ResourcesPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-center mb-10">
        <Library className="h-10 w-10 text-primary mr-3" />
        <h1 className="text-3xl font-bold text-center text-primary">Resource Library</h1>
      </div>
      <p className="text-center text-muted-foreground mb-10 max-w-2xl mx-auto">
        Explore curated articles, videos, and guides to support your mental health journey.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {PLACEHOLDER_RESOURCES.map((resource) => (
          <ResourceCard key={resource.id} resource={resource} />
        ))}
      </div>
    </div>
  );
}
